import { useRef, useState } from "react";
import { Constants } from "../utils/constants";
import { InsertUpdateMultipleDmsDetails } from "../service/mutations";
import StoreQueries from "../service/DataFetchQueries/storeQueries";
import dayjs from "dayjs";
import { traceSpan } from "../utils/OTTTracing";

const useDataFeedDmsList = (props: any) => {
  const {
    record,
    getStoreQuery,
    getDataFeed,
    setOpenSnackbar,
    setStatusMessage,
    setStatusMessageType,
    getDataFeedDmsList,
  } = props;
  const [originalRowData, setOriginalRowData] = useState<any>(null);
  const [saveNewValue, setsaveNewValue] = useState(false);
  const [openDeleteModal, setOpenDeleteModal] = useState(false);
  const [deleteNode, setDeleteNode] = useState<any>();
  const gridApiRef: any = useRef();
  let gridRef = useRef<any>();
  const gridColumnApiRef: any = useRef();


  const onRowEditingStarted = (params: any) => {
    traceSpan(`click_datafeededit_icon`, {
      event: `click_datafeededit_icon`,
      pageUrl: window.location.pathname,
      timestamp: new Date().toISOString(),
      userId:localStorage.getItem('user') || ''
    });
    params.api.refreshCells({
      columns: ["action"],
      rowNodes: [params.node],
      force: true,
    });
  };

  const onRowEditingStopped = (params: any) => {
    // traceSpan(`click_datafeedsave_icon`, {
    //   event: `click_datafeedsave_icon`,
    //   pageUrl: window.location.pathname,
    //   timestamp: new Date().toISOString(),
    //   userId:localStorage.getItem('user') || ''
    // });
    const { api, node } = params;
    const editedData = node.data;
    const originalData: any = originalRowData;
    if (originalData) {
      if (!saveNewValue) {
        node.setData(originalData);
      } else {
        const updatedData = { ...originalData, ...editedData };
        node.setData(updatedData);
        const input = {
          ...record,
          id: updatedData.id,
          dms: updatedData.dms,
          enterpriseCode: updatedData.enterpriseCode,
          serverName: updatedData.serverName,
          companyNumber: updatedData.companyNumber,
          dealerId: updatedData.dealerId,
          effectiveDate: updatedData.effectiveDate ? dayjs(updatedData.effectiveDate).format("MM/DD/YYYY") : null,
          action: "update",
        };
        updatedData !== originalData &&
          InsertUpdateMultipleDmsDetails(input)
            .then((response) => {
              if (response.statusCode === 1) {
                getStoreQuery();
                getDataFeed();
                setStatusMessage("Data feed details update successfully");
                setStatusMessageType(Constants.statusType.success);
              } else {
                getStoreQuery();
                getDataFeed();
                setStatusMessage(response.statusMsg);
                setStatusMessageType(Constants.statusType.error);
              }
            })
            .finally(() => {
              getDataFeedDmsList();
              setOpenSnackbar(true);
            });
      }
      setsaveNewValue(false);
    }
  };

  const onCellClicked = (params: any) => {
    const target = params.event.target;
    let action = target.closest("[data-action]")?.dataset.action;
    // Handle click event for action cells
    if (params.column.colId === "action" && action) {
      if (action === Constants.actions.edit) {
    //     traceSpan(`click_edit_icon`, {
    //   event: `click_edit_icon`,
    //   pageUrl: window.location.pathname,
    //   timestamp: new Date().toISOString(),
    //   userId:localStorage.getItem('user') || ''
    // });
        const node = params.node;
        const data = node.data;
        params.api.startEditingCell({
          rowIndex: params.node.rowIndex,
          // gets the first columnKey
          colKey:
            gridColumnApiRef?.current?.getDisplayedCenterColumns()[0].colId,
        });
        setOriginalRowData({ ...data });
      } else if (action === Constants.actions.delete) {
        traceSpan(`click_datafeeddelete_icon`, {
      event: `click_datafeeddelete_icon`,
      pageUrl: window.location.pathname,
      timestamp: new Date().toISOString(),
      userId:localStorage.getItem('user') || ''
    });
        const node = params.node;
        const data = node.data;
        setDeleteNode(data);
        setOpenDeleteModal(true);
      }
      if (action === Constants.actions.update) {
        traceSpan(`click_datafeedsave_icon`, {
      event: `click_datafeedsave_icon`,
      pageUrl: window.location.pathname,
      timestamp: new Date().toISOString(),
      userId:localStorage.getItem('user') || ''
    });
        setsaveNewValue(true);
        params.api.stopEditing(false);
      }
      if (action === Constants.actions.cancel) {
        traceSpan(`click_editdatafeedcancel_icon`, {
      event: `click_editdatafeedcancel_icon`,
      pageUrl: window.location.pathname,
      timestamp: new Date().toISOString(),
      userId:localStorage.getItem('user') || ''
    });
        params.api.stopEditing(true);
      }
    }
  };

  const onGridReady = (params: any) => {
    params?.api?.showLoadingOverlay();
    gridApiRef.current = params.api;
    gridRef.current = params.api;
    gridColumnApiRef.current = params.columnApi;
    getDataFeedDmsList();
  };

  const handleDialogClose = () => {
    traceSpan(`click_datafeeddeletecancel_button`, {
      event: `click_datafeeddeletecancel_button`,
      pageUrl: window.location.pathname,
      timestamp: new Date().toISOString(),
      userId:localStorage.getItem('user') || ''
    });
    setOpenDeleteModal(false);
  }

  const handleConfirm = () => {
    traceSpan(`click_datafeeddeleteconfirm_button`, {
      event: `click_datafeeddeleteconfirm_button`,
      pageUrl: window.location.pathname,
      timestamp: new Date().toISOString(),
      userId:localStorage.getItem('user') || ''
    });
    const input = {
      ...record,
      ...deleteNode,
      action: "delete",
    };   
      InsertUpdateMultipleDmsDetails(input)
        .then((response) => {
          if (response.statusCode === 1) {
            getStoreQuery();
            getDataFeed();
            setStatusMessage("Data feed details deleted successfully");
            setStatusMessageType(Constants.statusType.success);
          } else {
            getStoreQuery();
            getDataFeed();
            setStatusMessage(response.statusMsg);
            setStatusMessageType(Constants.statusType.error);
          }
        })
        .finally(() => {
          getDataFeedDmsList();
          setOpenSnackbar(true);
        });
    setOpenDeleteModal(false);
  };

  return {
    onRowEditingStopped,
    onRowEditingStarted,
    onCellClicked,
    onGridReady,
    gridApiRef,
    openDeleteModal,
    setOpenDeleteModal,
    handleDialogClose,
    handleConfirm,
  };
};

export default useDataFeedDmsList;
