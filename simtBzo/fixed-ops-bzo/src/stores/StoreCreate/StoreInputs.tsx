import * as React from "react";
import {
  TextInput,
  SelectInput,
  NumberInput,
  required,
  useTranslate,
  FormDataConsumer,
  regex,
} from "react-admin";
import { Stack, Box, Typography } from "@mui/material";
import { Constants } from "../../utils/constants";
import { DatePicker, DatePickerProps } from "antd";
import dayjs from "dayjs";
import { traceSpan } from "../../utils/OTTTracing";

export const StoreInputs = (props: any) => {
  const {
    storeRecord,
    action,
    sortOrder,
    data,
    manufacturers,
    estLaunchDate,
    setEstLaunchDate,
  } = props;
  const translate = useTranslate();
  const onDateChange: DatePickerProps["onChange"] = (date, dateString) => {
    traceSpan("select_addstore_datepicker", {
      event: "select_addstore_datepicker",
      pageUrl: window.location.pathname,
      timestamp: new Date().toISOString(),
      userId: localStorage.getItem("user") || "",
      value: dateString,
    });
    setEstLaunchDate(date);
  };
  return (
    <Box flex="1" mt={-1} width={600}>
      <Typography variant="h6" sx={{ marginY: "0px", fontWeight: "bold" }}>
        {action === Constants.actions.edit
          ? translate("TITLES.Edit_Store")
          : translate("TITLES.Add_Store")}
      </Typography>
      <TextInput
        sx={{ mt: 0, mb: 0 }}
        source="storeName"
        label="Store Name"
        fullWidth
        validate={[
          required(" "),
          regex(
            Constants.patterns.alphanumericStoreNameRegex,
            translate("VALIDATION_MESSAGES.ALPHANUMERIC_WITH_HYPHEN")
          ),
        ]}
        readOnly={action === Constants.actions.edit ? true : false}
        helperText={false}
        onBlur={() => {
          traceSpan("input_storename_text", {
            event: "input_storename_text",
            pageUrl: window.location.pathname,
            timestamp: new Date().toISOString(),
            userId: localStorage.getItem("user") || "",
          });
        }}
      />
      {/* <TextInput
        style={{ display: "none" }}
        source="displayName"
        label="Display Name"
        fullWidth
        validate={[
          required(" "),
          regex(
            Constants.patterns.addressRegex,
            translate("VALIDATION_MESSAGES.INVALID_INPUT")
          ),
        ]}
        helperText={false}
        onBlur={() => {
          traceSpan("input_displayname_text", {
            event: "input_displayname_text",
            pageUrl: window.location.pathname,
            timestamp: new Date().toISOString(),
            userId: localStorage.getItem("user") || "",
          });
        }}
      /> */}
      <TextInput
        source="storeDesc"
        label="Store Description"
        fullWidth
        validate={[
          required(" "),
          regex(
            Constants.patterns.addressRegex,
            translate("VALIDATION_MESSAGES.INVALID_INPUT")
          ),
        ]}
        helperText={false}
        onBlur={() => {
          traceSpan("input_storedescription_text", {
            event: "input_storedescription_text",
            pageUrl: window.location.pathname,
            timestamp: new Date().toISOString(),
            userId: localStorage.getItem("user") || "",
          });
        }}
      />
      <TextInput
        source="inSfStoreId"
        label="Store ID"
        fullWidth
        validate={[
          required(" "),
          regex(
            Constants.patterns.alphaNumericRegex,
            translate("VALIDATION_MESSAGES.INVALID_INPUT")
          ),
        ]}
        helperText={false}
        onBlur={() => {
          traceSpan("input_storeid_text", {
            event: "input_storeid_text",
            pageUrl: window.location.pathname,
            timestamp: new Date().toISOString(),
            userId: localStorage.getItem("user") || "",
          });
        }}
      />
      <SelectInput
        label="Manufacturer"
        source="manufacturer"
        choices={manufacturers}
        fullWidth
        optionValue="name"
        validate={required(" ")}
        helperText={false}
        onFocus={() => {
          traceSpan("manufacturer_dropdown_focused", {
            event: "manufacturer_dropdown_focused",
            pageUrl: window.location.pathname,
            timestamp: new Date().toISOString(),
            userId: localStorage.getItem("user") || "",
          });
        }}
        onChange={(e) => {
          traceSpan("select_manufacturer_dropdown", {
            event: "select_manufacturer_dropdown",
            pageUrl: window.location.pathname,
            timestamp: new Date().toISOString(),
            userId: localStorage.getItem("user") || "",
            value: e.target.value,
          });
        }}
      />
      <SelectInput
        label="DMS"
        fullWidth
        validate={required(" ")}
        source="dms"
        choices={data}
        optionText="dms"
        optionValue="dms"
        helperText={false}
        onFocus={() => {
          traceSpan("dms_dropdown_focused", {
            event: "dms_dropdown_focused",
            pageUrl: window.location.pathname,
            timestamp: new Date().toISOString(),
            userId: localStorage.getItem("user") || "",
          });
        }}
        onChange={(e) => {
          traceSpan("select_dms_dropdown", {
            event: "select_dms_dropdown",
            pageUrl: window.location.pathname,
            timestamp: new Date().toISOString(),
            userId: localStorage.getItem("user") || "",
            value: e.target.value,
          });
        }}
      />
      <NumberInput
        source="sortOrder"
        label="Sort Order"
        sx={{ width: "49%", display: "none" }}
        disabled
        defaultValue={sortOrder}
        validate={required(" ")}
        helperText={false}
        onBlur={() => {
          traceSpan("input_sortorder_text", {
            event: "input_sortorder_text",
            pageUrl: window.location.pathname,
            timestamp: new Date().toISOString(),
            userId: localStorage.getItem("user") || "",
          });
        }}
      />
      <FormDataConsumer<{ dms: string }>>
        {({ formData, ...rest }) =>
          formData.dms &&
          formData.dms !== "Dealertrack" && (
            <TextInput
              source="dealerId"
              label="Dealer Id"
              fullWidth
              validate={[
                required(" "),
                regex(
                  formData.dms === "Reynolds"
                    ? Constants.patterns.dealerId
                    : Constants.patterns.alphaNumericRegex,
                  formData.dms === "Reynolds"
                    ? "Invalid dealer id format for Reynolds"
                    : translate("VALIDATION_MESSAGES.ALPHANUMERIC")
                ),
              ]}
              helperText={false}
              onBlur={() => {
                traceSpan("input_dealerid_text", {
                  event: "input_dealerid_text",
                  pageUrl: window.location.pathname,
                  timestamp: new Date().toISOString(),
                  userId: localStorage.getItem("user") || "",
                });
              }}
            />
          )
        }
      </FormDataConsumer>
      <FormDataConsumer<{ dms: string }>>
        {({ formData, ...rest }) =>
          formData.dms === "Dealertrack" && (
            <>
              <Stack
                direction="row"
                display={"flex"}
                justifyContent={"space-between"}>
                <TextInput
                  source="companyNumber"
                  label="Company Number"
                  sx={{ width: "49%" }}
                  validate={[
                    required(" "),
                    regex(
                      Constants.patterns.alphaNumericRegex,
                      translate("VALIDATION_MESSAGES.ALPHANUMERIC")
                    ),
                  ]}
                  helperText={false}
                  onBlur={() => {
                    traceSpan("input_companynumber_text", {
                      event: "input_companynumber_text",
                      pageUrl: window.location.pathname,
                      timestamp: new Date().toISOString(),
                      userId: localStorage.getItem("user") || "",
                    });
                  }}
                />
                <TextInput
                  source="enterpriseCode"
                  label="Enterprise Code"
                  sx={{ width: "49%" }}
                  validate={[
                    required(" "),
                    regex(
                      Constants.patterns.alphaNumericRegex,
                      translate("VALIDATION_MESSAGES.ALPHANUMERIC")
                    ),
                  ]}
                  helperText={false}
                  onBlur={() => {
                    traceSpan("input_enterprisecode_text", {
                      event: "input_enterprisecode_text",
                      pageUrl: window.location.pathname,
                      timestamp: new Date().toISOString(),
                      userId: localStorage.getItem("user") || "",
                    });
                  }}
                />
              </Stack>
              <TextInput
                source="serverName"
                label="Server Name"
                fullWidth
                validate={[
                  required(" "),
                  regex(
                    Constants.patterns.alphaNumericRegex,
                    translate("VALIDATION_MESSAGES.ALPHANUMERIC")
                  ),
                ]}
                helperText={false}
                onBlur={() => {
                  traceSpan("input_servername_text", {
                    event: "input_servername_text",
                    pageUrl: window.location.pathname,
                    timestamp: new Date().toISOString(),
                    userId: localStorage.getItem("user") || "",
                  });
                }}
              />
            </>
          )
        }
      </FormDataConsumer>
      <TextInput
        source="dealerAddress"
        label="Address"
        fullWidth
        validate={[
          required(" "),
          regex(
            Constants.patterns.addressRegex,
            translate("VALIDATION_MESSAGES.INVALID_INPUT")
          ),
        ]}
        helperText={false}
        onBlur={() => {
          traceSpan("input_dealeraddress_text", {
            event: "input_dealeraddress_text",
            pageUrl: window.location.pathname,
            timestamp: new Date().toISOString(),
            userId: localStorage.getItem("user") || "",
          });
        }}
      />
      <TextInput
        source="stateCode"
        label="State Code"
        fullWidth
        validate={[
          required(" "),
          regex(
            Constants.patterns.alphaNumericRegex,
            translate("VALIDATION_MESSAGES.ALPHANUMERIC")
          ),
        ]}
        helperText={false}
        onBlur={() => {
          traceSpan("input_statecode_text", {
            event: "input_statecode_text",
            pageUrl: window.location.pathname,
            timestamp: new Date().toISOString(),
            userId: localStorage.getItem("user") || "",
          });
        }}
      />
      <p
        style={{
          fontSize: 12,
          marginLeft: "4px",
          marginBottom: 0,
          opacity: 0.8,
          color: "grey",
        }}>
        Estimated Launch Date
      </p>
      <DatePicker
        size={"large"}
        onChange={onDateChange}
        placeholder="Select Date"
        format={"MM/DD/YYYY"}
        value={estLaunchDate}
        variant="filled"
        style={{
          width: "100%",
          border: "none",
          borderBottom: "1px solid #958a8a",
          borderRadius: 0,
          padding: "11px",
        }}
        onFocus={() => {
          traceSpan("addstore_datepicker_focused", {
            event: "addstore_datepicker_focused",
            pageUrl: window.location.pathname,
            timestamp: new Date().toISOString(),
            userId: localStorage.getItem("user") || "",
          });
        }}></DatePicker>
      {/* {agreementDateMsg && (
        <p
          style={{
            fontSize: 12,
            color: "#d32f2f",
            margin: "6px 0 0 14px",
          }}
        >
          {agreementDateMsg}
        </p>
      )} */}
      <TextInput
        source="website"
        label="Website"
        type="website"
        fullWidth
        validate={[
          required(" "),
          regex(
            Constants.patterns.websiteUrlregex,
            translate("VALIDATION_MESSAGES.URL")
          ),
        ]}
        helperText={false}
        onBlur={() => {
          traceSpan("input_website_text", {
            event: "input_website_text",
            pageUrl: window.location.pathname,
            timestamp: new Date().toISOString(),
            userId: localStorage.getItem("user") || "",
          });
        }}
      />
    </Box>
  );
};
