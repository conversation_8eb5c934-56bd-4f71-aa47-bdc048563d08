import React, {
  useState,
  useRef,
  useEffect,
  useMemo,
  useCallback,
} from "react";
import {
  Box,
  Button,
  FormControlLabel,
  Grid,
  Radio,
  RadioGroup,
  Tooltip,
  Typography,
} from "@mui/material";
import { AgGridReact } from "ag-grid-react";
import "ag-grid-community/styles/ag-grid.css";
import "ag-grid-community/styles/ag-theme-alpine.css";
import { useTheme } from "@mui/material/styles";
import SplitPane, { Pane } from "split-pane-react";
import "split-pane-react/esm/themes/default.css";
import FileDownloadIcon from "@mui/icons-material/FileDownload";
import { useRecordContext, useTranslate } from "react-admin";
import {
  UpdateSubmitStatus,
  UpdateOpcodeCategorizationStatus,
} from "../../../service/mutations";
import { Constants } from "../../../utils/constants";
import SnackBarMessage from "../../../components/SnackBarMessage";
import CustomLinearProgress from "../../../components/CustomLinearProgress";
import { RowDragEndEvent, RowDragMoveEvent } from "ag-grid-community";
import { exportMultipleSheetsAsExcel } from "ag-grid-enterprise";
import OpcodeConfirmationModal from "../../../components/OpcodeConfirmationModal";
import {
  CategorizedColumnDefinition,
  UnCategorizedColumnDefinition,
} from "../../../types";
import OpcodeGridDefs from "./OpcodeGridDefs";
import { traceSpan } from "../../../utils/OTTTracing";

const OpCodesList = () => {
  const record = useRecordContext();
  const gridRef =
    useRef<
      AgGridReact<CategorizedColumnDefinition | UnCategorizedColumnDefinition>
    >(null);

  const [leftColumnApi, setLeftColumnApi] = useState<any>(null);
  const [leftRowDatanew, setLeftRowDatanew] = useState<any>();

  const [categorizedOpcodeStatusFinal, setCategorizedOpcodeStatusFinal] =
    useState(false);

  const translate = useTranslate();
  const containerStyle = useMemo(() => ({ width: "100%", height: "100%" }), []);
  const gridStyle = useMemo(() => ({ height: "100%", width: "100%" }), []);
  const theme = useTheme();
  const getRowId = (params: any) => params.data.id;
  const [sizes, setSizes] = useState<any>(["75%", "25%"]);
  const {
    leftColumnDefs,
    rightColumnDefs,
    defaultColDef,
    autoGroupColumnDef,
    showUncategorizedTable,
    opcodeCategorizationStatus,
    setShowUncategorizedTable,
    setLoading,
    fetchOpcodes,
    generatePValData,
    updateOpcodes,
    leftApi,
    rightApi,
    setLeftApi,
    setRightApi,
    leftRowData,
    setLeftRowData,
    rightRowData,
    setOpenSnackbar,
    openSnackbar,
    statusMessage,
    isSuccessful,
    setIsSuccessful,
    setStatusMessage,
    loading,
    rowData,
    selectedDepartment,
    handleRadioChange,
    setSelectedDepartment,
    categorizedOpcodeStatus,
    setCategorizedOpcodeStatus,
    opcodeCount,
  } = OpcodeGridDefs({ record });
  const gridItemProps = showUncategorizedTable
    ? { xs: 6, md: 6 }
    : { xs: 12, md: 12 };

  const updateColDef = useCallback(() => {
    const columnDefs = [...rightColumnDefs];
    if (categorizedOpcodeStatus && opcodeCategorizationStatus) {
      columnDefs[12].hide = true;
      columnDefs[4].editable = false;
      gridRef.current?.api?.setColumnDefs(columnDefs);
      setShowUncategorizedTable(false);
    } else {
      columnDefs[12].hide = false;
      columnDefs[4].editable = true;
      gridRef.current?.api?.setColumnDefs(columnDefs);
    }
  }, [opcodeCategorizationStatus, categorizedOpcodeStatus]);

  useEffect(() => {
    updateColDef();
  }, [opcodeCategorizationStatus, categorizedOpcodeStatus]);

  useEffect(() => {
    setLoading(true);
    fetchOpcodes();
  }, []);

  const onDragStop = useCallback(
    (params: any) => {
      const { nodes, overNode } = params;
      const category = overNode?.key || overNode?.parent?.key;

      if (category) {
        const pValData = nodes.map((node: any) =>
          generatePValData(node, category, selectedDepartment, "dragDrop")
        );
        params?.api?.showLoadingOverlay();
        updateOpcodes(pValData, record.tenantId, record.storeId, "");
      }
    },
    [leftApi, selectedDepartment]
  );
  const onRowDragEnd = useCallback(
    (params: any) => {
      if (params.api) {
        // Call onDragStop only if the drag operation is related to the grid
        onDragStop(params);
      }
    },
    [selectedDepartment]
  );

  useEffect(() => {
    if (!leftApi || !rightApi) {
      return;
    }
    const dropZoneParams = rightApi.getRowDropZoneParams({ onRowDragEnd });
    leftApi.removeRowDropZone(dropZoneParams);
    leftApi.addRowDropZone(dropZoneParams);
  }, [leftApi, rightApi, onRowDragEnd]);

  const onGridReady = (params: any, side: any) => {
    if (side === 0) {
      setLeftApi(params.api);
      setLeftColumnApi(params.columnApi);
    }
    if (side === 1) {
      setRightApi(params.api);
    }
  };

  const sideBar = useMemo(() => {
    return {
      toolPanels: [
        {
          id: "columns",
          labelDefault: "Columns",
          labelKey: "columns",
          iconKey: "columns",
          toolPanel: "agColumnsToolPanel",
          toolPanelParams: {
            suppressRowGroups: true,
            suppressValues: true,
            suppressPivotMode: true,
            suppressColumnSelectAll: true,
          },
        },
      ],
    };
  }, []);

  const toggleUncategorized = async () => {
    setShowUncategorizedTable((prevShowLeftTable: any) => !prevShowLeftTable);
    setSizes(["50%", "50%"]);
  };

  const CustomLoadingOverlay = (props: any) => {
    return (
      <div
        className="ag-overlay-loading-center"
        style={{ backgroundColor: "lightsteelblue", height: "9%" }}>
        <i className="fas fa-hourglass-half"> {props.loadingMessage} </i>
      </div>
    );
  };

  const loadingOverlayComponent = useMemo(() => {
    return CustomLoadingOverlay;
  }, []);

  const loadingOverlayComponentParams = useMemo(() => {
    return {
      loadingMessage: translate("GENERAL.LOADING_TEXT"),
    };
  }, []);

  const onRowDragMove = useCallback(
    (event: RowDragMoveEvent) => {
      const movingNode = event.node!;
      const overNode = event.overNode!;
      if (movingNode.group) {
        console.log("Dragging an entire group:", movingNode.key);
      } else {
        console.log("Dragging a row inside a group:", movingNode.data);
        let groupCountry;
        if (overNode.group) {
          groupCountry = overNode.key;
        } else {
          groupCountry = overNode?.data?.opcategory;
        }
        const needToChangeParent =
          movingNode?.data?.opcategory !== groupCountry;
        if (needToChangeParent) {
          onRowDragEnd(event as RowDragEndEvent);
        } else {
        }
      }
    },
    [selectedDepartment]
  );
  const onCellEditingStopped = useCallback((params: any) => {
    if (params.oldValue !== params.newValue) {
      const node = params.node;
      const pValData = [
        generatePValData(
          node,
          node.data.opcategory,
          null,
          "editCell",
          params.oldValue
        ),
      ];

      params?.api?.showLoadingOverlay();
      updateOpcodes(
        pValData,
        record.tenantId,
        record.storeId,
        "editCell",
        params
      );
    }
  }, []);

  // Comparator function to sort categories in descending order
  const initialGroupOrderComparator = useCallback((params: any) => {
    const a = params.nodeA.key || "";
    const b = params.nodeB.key || "";
    return b.localeCompare(a);
  }, []);

    const handleRowExpansion = (event: any) => {
    const rowData = event.data;
    const isExpanded = event.expanded;
    
    traceSpan(`opcode_row_expansion`, {
      event: `opcode_row_expansion`,
      pageUrl: window.location.pathname,
      timestamp: new Date().toISOString(),
      userId: localStorage.getItem('user') || '',
      action: isExpanded ? 'expand' : 'collapse',
      rowIndex: event.rowIndex
    });
  };

  const handleRowClick = (event: any) => {
    const rowData = event.data;
    traceSpan(`opcode_row_click`, {
      event: `opcode_row_click`,
      pageUrl: window.location.pathname,
      timestamp: new Date().toISOString(),
      userId: localStorage.getItem('user') || '',
      rowIndex: event.rowIndex
    });
  };

  const getGridWrapper = (id: number) => (
    <div
      className={Constants.ag_grid_theme}
      style={{ height: "70vh", width: "100%" }}>
      <AgGridReact
        defaultColDef={defaultColDef}
        getRowId={getRowId}
        rowDragEntireRow={true}
        rowDragText={(params: any, dragItemCount: any) => {
          if (dragItemCount > 1) {
            return dragItemCount + " Opcodes";
          }
          return params?.rowNode?.data?.lbropcode;
        }}
        onRowDragEnd={(params: any) =>
          opcodeCategorizationStatus && onRowDragMove(params)
        }
        rowDragMultiRow={true}
        animateRows={true}
        onGridReady={(params) => onGridReady(params, id)}
        ref={gridRef}
        autoGroupColumnDef={autoGroupColumnDef}
        // rowSelection={id === 0 ? "multiple" : undefined}
        rowSelection={"multiple"}
        suppressMoveWhenRowDragging={id === 0}
        rowData={id === 0 ? leftRowData : rightRowData}
        columnDefs={id === 0 ? leftColumnDefs : rightColumnDefs}
        groupDisplayType={id === 0 ? "singleColumn" : undefined}
        sideBar={sideBar}
        loadingOverlayComponent={loadingOverlayComponent}
        loadingOverlayComponentParams={loadingOverlayComponentParams}
        onCellEditingStopped={onCellEditingStopped}
        initialGroupOrderComparator={initialGroupOrderComparator}
        rowHeight={23}
        getRowHeight={(params: any) => {
          if (params.node.group) {
            return 45; // Set custom height for group rows
          }
          return 23; // Default row height for data rows
        }}
        getRowStyle={(params: any) => {
          return {
            backgroundColor: params.node.group
              ? "rgba(0,61,107, 0.2)"
              : "white",
            fontWeight: params.node.group ? "bold" : "normal",
            border: "1px solid white",
          };
        }}
        onFilterChanged={onFilterChanged}
        onSortChanged={onSortChanged}
        onRowClicked={handleRowClick}
        onRowGroupOpened={handleRowExpansion}
      />
      <SnackBarMessage
        onClose={() => setOpenSnackbar(false)}
        open={openSnackbar}
        message={statusMessage}
        type={isSuccessful ? "success" : "error"}
      />
    </div>
  );

  const handleExport = (api: any, fileName: string, sheetName: string) => {
    const spreadsheets: any[] = [];
    spreadsheets.push(api.getSheetDataForExcel({ sheetName }));

    exportMultipleSheetsAsExcel({
      data: spreadsheets,
      fileName,
    });
  };

  const onBtnUncategorizedExport = () => {
    
    traceSpan(`download_excel_categorized`, {
              event: `download_categorized`,
              pageUrl: window.location.pathname,
              timestamp: new Date().toISOString(),
              userId: localStorage.getItem('user') || ''
            });
    handleExport(leftApi!, "UnCategorized.xlsx", "Opcodes");
  };

  const onBtnCategorizedExport = () => {
    handleExport(rightApi!, "Categorized.xlsx", "Opcodes");
  };

  const handleSaveChanges = async () => {
    traceSpan(`click_save_button`, {
      event: `click_save_button`,
      pageUrl: window.location.pathname,
      timestamp: new Date().toISOString(),
      userId:localStorage.getItem('user') || ''
    });
    try {
      const result = await UpdateOpcodeCategorizationStatus(
        localStorage.getItem("dmsType"),
        record.storeId,
        record.tenantId
      );
      if (result && result.string) {
        const statusMessage = translate(result?.string);
        setStatusMessage(statusMessage);
        setOpenSnackbar(true);
        setCategorizedOpcodeStatus(true);
      }
    } catch (error) {
      const statusMessage = translate("ERROR_MESSAGES.SOMETHING_WENT_WRONG");
      setStatusMessage(statusMessage);
      setOpenSnackbar(true);
      console.log(error);
    }
  };

  const submitOpcodeDetails = async () => {
    try {
      const result = await UpdateSubmitStatus(
        record.tenantId,
        record.storeId,
        "opcode"
      );
      if (result?.results[0]?.status === 1) {
        setIsSuccessful(true);
        const statusMessage = translate(result?.results[0]?.msg);
        setStatusMessage(statusMessage);
        setOpenSnackbar(true);
        fetchOpcodes();
      }
    } catch (error) {
      setIsSuccessful(false);
      const statusMessage = translate("ERROR_MESSAGES.SOMETHING_WENT_WRONG");
      setStatusMessage(statusMessage);
      setOpenSnackbar(true);
      console.log(error);
    }
  };

  if (loading) return <CustomLinearProgress />;

  const onFilterChanged = (e: any) => {
        const filterValues = e.api.getFilterModel();
        Object.keys(filterValues).forEach((colId) => {
          traceSpan(`filter_grid_${colId}`, {
            event: `filter_grid_${colId}`,
          pageUrl: window.location.pathname,
          timestamp: new Date().toISOString(),
          userId: localStorage.getItem("user") || "",
          column: colId,
          filterValue: JSON.stringify(filterValues[colId])
          });
        });
      };
    const onSortChanged = (params: any) => {
      // Get column states and filter only sorted columns
      const sortModel = params.columnApi.getColumnState()
        .filter((col: any) => col.sort != null)
        .map((col: any) => ({
          colId: col.colId,
          sort: col.sort
        }));
    
      sortModel.forEach((sortItem: { colId: string; sort: 'asc' | 'desc' }) => {
        traceSpan(`sort_grid_${sortItem.colId}`, {
           event: `sort_grid_${sortItem.colId}`,
        pageUrl: window.location.pathname,
        timestamp: new Date().toISOString(),
        userId: localStorage.getItem('user') || '',
        column: sortItem.colId,
        direction: sortItem.sort
        });
      });
    };

  return (
    <Box style={{ width: "100%", height: "100%" }}>
      {/* {rowData?.length === 0 ? (
        <div style={{ width: "100%" }}>
          <Box style={{ padding: 25 }}>
            <Typography variant="body1" align="center" style={{ padding: 25 }}>
              No Opcodes Available
            </Typography>
          </Box>
        </div>
      ) : ( */}
      <>
        {!opcodeCategorizationStatus && (
          <div
            style={{
              height: "3vh",
              width: "100%",
              position: "relative",
              marginTop: "-20px",
            }}>
            <Tooltip
              title={
                showUncategorizedTable
                  ? translate("GENERAL.HIDE_UNCATEGORIZED_TABLE")
                  : translate("GENERAL.SHOW_UNCATEGORIZED_TABLE")
              }>
              <Button
                size="small"
                variant="contained"
                color="primary"
                onClick={() => {
                   const action = showUncategorizedTable ? "hide" : "show";
                    traceSpan(`click_${action}_button`, {
                      event: `click_${action}_button`,
                      pageUrl: window.location.pathname,
                      timestamp: new Date().toISOString(),
                      userId: localStorage.getItem("user") || ""
                    });
                  setSelectedDepartment("Service");
                  toggleUncategorized();
                }}
                style={{
                  position: "absolute",
                  top: 0,
                  right: 0,
                  transform: "translateY(-50%)",
                  textTransform: "none",
                  fontSize: "12px",
                }}>
                {showUncategorizedTable
                  ? translate("BUTTONS.HIDE")
                  : translate("BUTTONS.SHOW")}
              </Button>
            </Tooltip>
            <OpcodeConfirmationModal
              submitOpcodeDetails={submitOpcodeDetails}
              RowData={rowData}
              isDisabled={opcodeCategorizationStatus}
            />
          </div>
        )}

        {!categorizedOpcodeStatus && opcodeCategorizationStatus && (
          <div
            style={{
              height: "3vh",
              width: "100%",
              position: "relative",
              marginTop: "-20px",
            }}>
            <Tooltip
              title={
                showUncategorizedTable
                  ? translate("GENERAL.HIDE_UNCATEGORIZED_TABLE")
                  : translate("GENERAL.SHOW_UNCATEGORIZED_TABLE")
              }>
              <Button
                size="small"
                variant="contained"
                color="primary"
                onClick={() => {
                  setSelectedDepartment("Service");
                  toggleUncategorized();
                }}
                style={{
                  position: "absolute",
                  top: 0,
                  right: 0,
                  transform: "translateY(-50%)",
                  textTransform: "none",
                  fontSize: "12px",
                }}>
                {showUncategorizedTable
                  ? translate("BUTTONS.HIDE")
                  : translate("BUTTONS.SHOW")}
              </Button>
            </Tooltip>
            <Tooltip title={translate("GENERAL.SAVE_CHANGES")}>
              <Button
                size="small"
                variant="contained"
                color="primary"
                disabled={opcodeCount.uncategorizedCount == 0 ? false : true}
                onClick={() => handleSaveChanges()}
                style={{
                  position: "absolute",
                  top: 0,
                  right: "68px",
                  transform: "translateY(-50%)",
                  textTransform: "none",
                  fontSize: "12px",
                }}>
                {translate("BUTTONS.SAVE_CHANGES")}
              </Button>
            </Tooltip>
          </div>
        )}
        <Box
          sx={{
            display: "flex",
            justifyContent: "space-between",
            marginTop: -1,
          }}>
          {showUncategorizedTable && (
            <div
              style={{
                display: "flex",
                alignItems: "center",
                justifyContent: "space-between",
                width: "50%",
              }}>
              <Box>
                <Tooltip title="Export To Excel">
                  <div>
                    <FileDownloadIcon
                      onClick={onBtnUncategorizedExport}
                      style={{ color: theme.palette.primary.main }}
                    />
                  </div>
                </Tooltip>
              </Box>
              <RadioGroup
                sx={{
                  fontWeight: "bold",
                  display: "flex",
                  flexDirection: "row",
                  gap: 1,
                }}
                row
                aria-labelledby="demo-row-radio-buttons-group-label"
                name="row-radio-buttons-group"
                value={selectedDepartment}
                onChange={handleRadioChange}>
                <Typography
                  variant="body2"
                  sx={{ fontSize: "14px", fontWeight: "bold", mr: 2 }}>
                  Select department:{" "}
                </Typography>
                <FormControlLabel
                  control={
                    <Radio
                      sx={{
                        "& .MuiSvgIcon-root": {
                          fontSize: 16,
                        },
                        p: 0.25,
                      }}
                    />
                  }
                  label={
                    <Typography variant="body2" sx={{ fontSize: "12px" }}>
                      Service
                    </Typography>
                  }
                  value="Service"
                />
                <FormControlLabel
                  control={
                    <Radio
                      sx={{
                        "& .MuiSvgIcon-root": {
                          fontSize: 16,
                        },
                        p: 0.25,
                      }}
                    />
                  }
                  label={
                    <Typography variant="body2" sx={{ fontSize: "12px" }}>
                      Body Shop
                    </Typography>
                  }
                  value="Body Shop"
                />
              </RadioGroup>
            </div>
          )}
          <div
            style={{
              display: "flex",
              justifyContent: "space-between",
              alignItems: "center",
              marginTop: "10px",
            }}>
            <Tooltip title="Export To Excel">
              <div style={{ cursor: "pointer" }}>
                <FileDownloadIcon
                  onClick={onBtnCategorizedExport}
                  style={{ color: theme.palette.primary.main }}
                />
              </div>
            </Tooltip>
          </div>
        </Box>
        {opcodeCategorizationStatus && showUncategorizedTable && (
          <div style={{ height: "70vh" }}>
            <SplitPane
              sashRender={() => <div />}
              split="vertical"
              sizes={sizes}
              onChange={setSizes}>
              <Pane minSize={15} maxSize="85%">
                <Box>
                  <Box style={{ marginBottom: "10px" }}>
                    {getGridWrapper(0)}
                  </Box>
                </Box>
              </Pane>
              <Pane minSize={15} maxSize="85%">
                <div style={{ marginLeft: "10px" }}>
                  <Box style={{ marginBottom: "10px" }}>
                    {getGridWrapper(1)}
                  </Box>
                </div>
              </Pane>
            </SplitPane>
          </div>
        )}
        {!showUncategorizedTable && (
          <>
            <Grid item {...gridItemProps}>
              <div style={containerStyle}>
                <div style={gridStyle} className={Constants.ag_grid_theme}>
                  {getGridWrapper(1)}
                </div>
              </div>
            </Grid>
          </>
        )}
      </>
      {/* )} */}
      <div
        style={{
          display: "flex", //showUncategorizedTable ? "none" : "flex",
          gap: "40px",
          color: theme.palette.primary.main,
        }}>
        <div
          style={{
            marginLeft: "15px",
          }}>
          <strong>All: </strong>
          <span style={{ color: "#000" }}>{opcodeCount.totalCount}</span>
        </div>
        <div>
          <strong>Categorized: </strong>
          <span style={{ color: "#000" }}>{opcodeCount.categorizedCount}</span>
        </div>
        <div>
          <strong>Uncategorized: </strong>
          <span style={{ color: "#000" }}>
            {opcodeCount.uncategorizedCount}
          </span>
        </div>
      </div>
    </Box>
  );
};

export default OpCodesList;
