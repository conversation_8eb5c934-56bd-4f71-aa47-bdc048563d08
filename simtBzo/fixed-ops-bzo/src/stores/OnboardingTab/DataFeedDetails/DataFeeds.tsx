import {
  Box,
  <PERSON>ton,
  Card,
  Dialog,
  Table,
  TableBody,
  TableCell,
  TableRow,
  Typography,
} from "@mui/material";
import React, { useEffect, useState } from "react";
import useOnboarding from "../../../CustomHooks/useOnboarding";
import { useGetList, useRecordContext } from "react-admin";
import CustomLinearProgress from "../../../components/CustomLinearProgress";
import { Constants } from "../../../utils/constants";
import SnackBarMessage from "../../../components/SnackBarMessage";
import AddNewDataFeed from "./AddNewDataFeed";
import DataFeedList from "./DataFeedList";
import StoreQueries from "../../../service/DataFetchQueries/storeQueries";
import { traceSpan } from "../../../utils/OTTTracing";

const DataFeeds = (props: any) => {
  const { data } = useGetList("dmsMasters");
  const { getStoreQuery, dataFeedRecord } = props;
  const record = useRecordContext();
  const [openNewForm, setOpenNewForm] = useState(false);
  const [openSnackbar, setOpenSnackbar] = useState(false);
  const [statusMessage, setStatusMessage] = useState<any>("");
  const [statusMessageType, setStatusMessageType] = React.useState<any>("");
  const [allLoadList, setAllLoadList] = useState([]);
  const { getDataFeed, dataFeedDetails, dataFeedLoading } = useOnboarding({
    record: record,
  });
  const { GetSchedulerDetails, GetStoreWithDiffDmsQuery } = StoreQueries;
  const [loadingDmsDetails, setLoadingDmsDetails] = useState(false);

  const dms =
    record.dms === "Dealertrack"
      ? "dtk"
      : record.dms === "CDK Global"
      ? "cdk"
      : record.dms === "Automate"
      ? "atm"
      : record.dms === "Reynolds"
      ? "Reynolds"
      : record.dms;
  const dataKeysName = Constants.DATA_FEEDS[dms];

  useEffect(() => {
    getDataFeed();
  }, []);

  if (dataFeedLoading) return <CustomLinearProgress />;

  const GetDmsDetails = () => {
    traceSpan(`click_fetchdmsdetails_button`, {
      event: `click_fetchdmsdetails_button`,
      pageUrl: window.location.pathname,
      timestamp: new Date().toISOString(),
      userId: localStorage.getItem("user") || "",
    });
    GetSchedulerDetails(record.sfStoreId)
      .then((res: any) => {
        if (res === "success") {
          setOpenSnackbar(true);
          setStatusMessage("DMS details fetched successfully");
          setStatusMessageType(Constants.statusType.success);
          getDataFeed();
        } else {
          setOpenSnackbar(true);
          setStatusMessage(res);
          setStatusMessageType(Constants.statusType.error);
        }
        setLoadingDmsDetails(false);
      })
      .finally(() => {
        getDataFeedDmsList();
      });
  };

  const getDataFeedDmsList = () => {
    GetStoreWithDiffDmsQuery(record.storeId).then((res: any) => {
      setAllLoadList(res);
    });
  };

  return (
    <>
      <Box>
        <Box
          sx={{
            display: "flex",
            justifyContent: "space-between",
            mt: 3,
            ml: 1,
          }}>
          <Typography
            sx={{
              fontSize: 16,
              color: "#003d6b",
              fontWeight: "bold",
            }}>
            Details
          </Typography>
          <Box>
            <Button
              color="primary"
              variant="contained"
              onClick={() => {
                setLoadingDmsDetails(true);
                GetDmsDetails();
              }}
              disabled={loadingDmsDetails}
              sx={{
                textTransform: "none",
                mr: 1,
              }}
              // hide for store launched status
            >
              Fetch DMS Details
            </Button>
            <Button
              color="primary"
              variant="contained"
              onClick={() => {
                traceSpan(`click_addnewdatafeeds_button`, {
                  event: `click_addnewdatafeeds_button`,
                  pageUrl: window.location.pathname,
                  timestamp: new Date().toISOString(),
                  userId: localStorage.getItem("user") || "",
                });
                setOpenNewForm(true);
              }}
              sx={{
                textTransform: "none",
              }}
              // hide for store launched status
              // disabled={record.storeLaunched === "Completed"}
            >
              Add New Data Feeds
            </Button>
          </Box>
        </Box>

        <Card
          sx={{
            mt: 1,
            border: "2px solid rgb(224, 224, 227)",
            boxShadow: "none",
          }}>
          <Table size="small">
            <TableBody>
              {Object.entries(dataKeysName).map(([key, value]: any) => {
                return (
                  <TableRow key={key}>
                    <TableCell sx={{ width: "30%" }}>{value}</TableCell>
                    <TableCell sx={{ width: "80%" }}>
                      {dataFeedDetails[key]}
                    </TableCell>
                  </TableRow>
                );
              })}
              {/*    } */}
            </TableBody>
          </Table>
        </Card>
        <Dialog open={openNewForm} maxWidth={false}>
          <AddNewDataFeed
            setOpenNewForm={setOpenNewForm}
            setStatusMessage={setStatusMessage}
            setOpenSnackbar={setOpenSnackbar}
            dataKeysName={dataKeysName}
            data={data}
            dataFeedDetails={dataFeedDetails}
            record={record}
            getStoreQuery={getStoreQuery}
            getDataFeed={getDataFeed}
            setStatusMessageType={setStatusMessageType}
            getDataFeedDmsList={getDataFeedDmsList}
          />
        </Dialog>
        <SnackBarMessage
          onClose={() => setOpenSnackbar(false)}
          open={openSnackbar}
          message={statusMessage}
          type={statusMessageType}
        />
      </Box>
      <DataFeedList
        record={dataFeedRecord}
        // allLoadList={allLoadList}
        setOpenSnackbar={setOpenSnackbar}
        setStatusMessage={setStatusMessage}
        getStoreQuery={getStoreQuery}
        getDataFeed={getDataFeed}
        setStatusMessageType={setStatusMessageType}
        allLoadList={allLoadList}
        setAllLoadList={setAllLoadList}
        getDataFeedDmsList={getDataFeedDmsList}
      />
    </>
  );
};

export default DataFeeds;
