import { <PERSON>, Button, CircularProgress } from "@mui/material";
import React, { useState } from "react";
import { Form, SaveButton, useTranslate } from "react-admin";
import CancelIcon from "@mui/icons-material/Cancel";
import { InsertUpdateMultipleDmsDetails } from "../../../service/mutations";
import { NewDataFeedInputs } from "../NewDataFeedInputs";
import { Constants } from "../../../utils/constants";
import dayjs from "dayjs";
import { traceSpan } from "../../../utils/OTTTracing";

const AddNewDataFeed = (props: any) => {
  const {
    setOpenNewForm,
    setStatusMessage,
    setOpenSnackbar,
    data,
    dataFeedDetails,
    getStoreQuery,
    getDataFeed,
    setStatusMessageType,
    getDataFeedDmsList
  } = props;
  const [effectiveDate, setEffectiveDate] = useState<any>(null);
  const [updating, setUpdating] = useState(false);
  const translate = useTranslate();
  const handleSubmitDataFeeds = async (values: any) => {
    traceSpan(`click_adddatafeeds_button`, {
      event: `click_adddatafeeds_button`,
      pageUrl: window.location.pathname,
      timestamp: new Date().toISOString(),
      userId:localStorage.getItem('user') || ''
    });
    setUpdating(true);
    const input = {
      ...dataFeedDetails,
      ...values,
      storeName: values.storeName,
      dms: values.Dms,
      enterpriseCode: values.EnterpriseCode,
      serverName: values.ServerName,
      companyNumber: values.CompanyNumber,
      dealerId: values.DealerId,
      effectiveDate: dayjs(effectiveDate).format("MM/DD/YYYY"),
      action: "insert",
    };
    InsertUpdateMultipleDmsDetails(input)
      .then((response) => {
        if (response.statusCode === 1) {
          getStoreQuery();
          getDataFeed();
          setOpenNewForm(false);
          setStatusMessage("New data feed details added successfully");
          setStatusMessageType(Constants.statusType.success);
        } else {
          getStoreQuery();
          getDataFeed();
          setOpenNewForm(false);
          setStatusMessage(response.statusMsg);
          setStatusMessageType(Constants.statusType.error);
        }
      })
      .finally(() => {
        setUpdating(false);
        getDataFeedDmsList();
        setOpenSnackbar(true);
      });
  };

  return (
    <Box
      sx={{
        position: "relative",
        opacity: updating ? 0.5 : 1,
        padding: "15px",
      }}
    >
      <Form onSubmit={handleSubmitDataFeeds} defaultValues={{}}>
        <NewDataFeedInputs
          effectiveDate={effectiveDate}
          setEffectiveDate={setEffectiveDate}
          data={data}
        />
        <Box>
          <SaveButton label={"Add"} />
          <Button
            color="primary"
            variant="contained"
            startIcon={<CancelIcon />}
            sx={{ m: 2 }}
            onClick={() => {
              traceSpan(`click_canceldatafeed_button`, {
                event: `click_canceldatafeed_button`,
                pageUrl: window.location.pathname,
                timestamp: new Date().toISOString(),
                userId:localStorage.getItem('user') || ''
              });
              setOpenNewForm(false)}}
          >
            {"CANCEL"}
          </Button>
          {updating && (
            <CircularProgress
              size={24}
              sx={{
                position: "absolute",
                top: "50%",
                left: "50%",
                marginTop: "-12px",
                marginLeft: "-12px",
              }}
            />
          )}
        </Box>
      </Form>
    </Box>
  );
};

export default AddNewDataFeed;
