import * as React from "react";
import { useState, ChangeEvent, useEffect } from "react";
import {
  Avatar,
  Box,
  Button,
  Card,
  CardContent,
  Divider,
  IconButton,
  Tab,
  Tabs,
  <PERSON>ltip,
  Typography,
} from "@mui/material";
import { useParams } from "react-router-dom";
import { TabPanel } from "../Layout/TabPanel";
import { OnBoarding } from "./OnboardingTab/Onboarding";
import { RecordContextProvider, useTranslate } from "react-admin";
import BorderColorIcon from "@mui/icons-material/BorderColor";
import InfoIcon from "@mui/icons-material/Info";
import { PageRoutes } from "../utils/pageRoutes";
import { Link as RouterLink } from "react-router-dom";
import Advisors from "./Advisors/Advisors";
import LaborGrid from "./LaborGrid/LaborGrid";
import { StoreSettings } from "./StoreSettings";
import Technicians from "./Technicians";
import BackButton from "../Layout/BackButton";
import PartsMatrix from "./PartsMatrix/PartsMatrix";
import useOnboarding from "../CustomHooks/useOnboarding";
import Loading from "../components/Loading";
import Models from "./Models/Models";
import type { DatePickerProps } from "antd";
import { DatePicker, Space } from "antd";
import dayjs from "dayjs";
import AvailableMenu from "./MenuSetUp/AvailableMenu";
import DeleteStore from "./storeDeleteButton";
import useStoreCreate from "../CustomHooks/useStoreCreate";
import SnackBarMessage from "../components/SnackBarMessage";
import ActivityLog from "../ActivityLog/ActivityLog";
import RevenueStatement from "./RevenueStatement/RevenueStatement";
import { traceSpan } from "../utils/OTTTracing";

export const StoreShow = () => {
  const { id } = useParams();
  const { getStoreQuery, storeRecord, isLoading } = useOnboarding({ id: id });
  const { handleSubmit, openSnackbar, statusMessage, setOpenSnackbar } =
    useStoreCreate({ action: "launchDate" });
  const tenantTableId = localStorage.getItem("tenant-id");
  const translate = useTranslate();
  const [tabValue, setTabValue] = useState(0);
  const [showDateSaveButton, setShowDateSaveButton] = useState(false);
  const handleTabChange = (event: ChangeEvent<{}>, newValue: number) => {
    const target = event.target as HTMLElement;
    const clickedTab = target.closest('[role="tab"]') as HTMLElement;
    const tabLabel = clickedTab?.textContent || clickedTab?.innerText || "";
    traceSpan(`click_${tabLabel}_tab`, {
      event: `click_${tabLabel}_tab`,
      pageUrl: window.location.pathname,
      timestamp: new Date().toISOString(),
      userId: localStorage.getItem("user") || "",
    });
    setTabValue(newValue);
  };
  const [selectedDate, setSelectedDate] = useState<any>(null);

  useEffect(() => {
    getStoreQuery();
  }, []);

  useEffect(() => {
    storeRecord?.storeLaunchedDate &&
      setSelectedDate(dayjs(storeRecord?.storeLaunchedDate));
  }, [storeRecord]);

  const onDateChange: DatePickerProps["onChange"] = (date, dateString) => {
    traceSpan("select_launchorestimatedlaunch_datepicker", {
      event: "select_launchorestimatedlaunch_datepicker",
      pageUrl: window.location.pathname,
      timestamp: new Date().toISOString(),
      userId: localStorage.getItem("user") || "",
      value: date,
    });
    setSelectedDate(date);
    setShowDateSaveButton(true);
  };
  const updateEstimatedDate = () => {
    traceSpan(`click_estimateddatesave_button`, {
      event: `click_estimateddatesave_button`,
      pageUrl: window.location.pathname,
      timestamp: new Date().toISOString(),
      userId: localStorage.getItem("user") || "",
    });
    let submitdata = {
      ...storeRecord,
      storeLaunchedDate: selectedDate
        ? dayjs(selectedDate).format("MM/DD/YYYY")
        : null,
    };
    handleSubmit(submitdata);
    setShowDateSaveButton(false);
  };

  const EstLaunchDate = () => {
    return (
      <Box sx={{ mb: "16px", display: "flex", alignItems: "flex-end" }}>
        <Space direction="vertical">
          <Box sx={{ display: "flex", flexDirection: "column" }}>
            <Typography
              sx={{
                color:
                  storeRecord.storeLaunched !== "Completed"
                    ? "#1976D2"
                    : "grey",
                fontSize: "12px",
              }}>
              {storeRecord.storeLaunched === "Completed"
                ? "Launched Date"
                : " Estimated Launch Date"}
            </Typography>
            <DatePicker
              disabled={storeRecord.storeLaunched === "Completed"}
              onChange={onDateChange}
              // placeholder="Estimated Launch Date"
              format={"MM/DD/YYYY"}
              value={selectedDate}
            />
          </Box>
        </Space>
        {showDateSaveButton && storeRecord.storeLaunched !== "Completed" && (
          <Button
            onClick={updateEstimatedDate}
            variant="contained"
            size="small"
            sx={{ marginLeft: "16px", mb: "2px" }}>
            Save
          </Button>
        )}
      </Box>
    );
  };

  if (isLoading) return <Loading />;
  return (
    <RecordContextProvider value={storeRecord}>
      <BackButton
        onClick={() => {
          traceSpan(`click_storeback_button`, {
            event: `click_storeback_button`,
            pageUrl: window.location.pathname,
            timestamp: new Date().toISOString(),
            userId: localStorage.getItem("user") || "",
          });
        }}
        url={
          tenantTableId
            ? PageRoutes.tenantShowPageRoute(tenantTableId)
            : PageRoutes.allStores
        }        
      />
      <SnackBarMessage
        onClose={() => setOpenSnackbar(false)}
        open={openSnackbar}
        message={statusMessage}
      />
      <Box mt={2} display="flex">
        <Box flex="5">
          <Card>
            <CardContent>
              <Box display="flex">
                <Avatar
                  src={storeRecord.dmsImg}
                  alt={storeRecord.storeName}
                  sx={{ marginX: 1, width: "50px", height: "50px" }}
                />
                <Box ml={2} flex="1">
                  <Typography variant="h5">
                    {storeRecord.storeName}
                    <Tooltip title={translate("TITLES.Edit_Store")}>
                      <IconButton
                        component={RouterLink}
                        to={PageRoutes.editStoreRoute(storeRecord.id)}
                        onClick={() => {
                          traceSpan(`click_editstore_button`, {
                            event: `click_editstore_button`,
                            pageUrl: window.location.pathname,
                            timestamp: new Date().toISOString(),
                            userId: localStorage.getItem("user") || "",
                          });
                        }}
                        state={{ record: storeRecord, action: "edit" }}
                        color="primary">
                        <BorderColorIcon color="primary" fontSize="small" />
                      </IconButton>
                    </Tooltip>
                    <Tooltip title={"Store Info"}>
                      <IconButton
                        onClick={() => {
                          traceSpan(`click_storeinfo_button`, {
                            event: `click_storeinfo_button`,
                            pageUrl: window.location.pathname,
                            timestamp: new Date().toISOString(),
                            userId: localStorage.getItem("user") || "",
                          });
                          setTabValue(7);
                        }}>
                        <InfoIcon color="primary" fontSize="small" />
                      </IconButton>
                    </Tooltip>
                  </Typography>
                  <Typography>{storeRecord.storeDesc}</Typography>
                </Box>
                <Box
                  display="flex"
                  alignItems="center"
                  justifyContent="flex-end"
                  flex="">
                  {/* <DeleteStore /> */}
                  <EstLaunchDate />
                </Box>
              </Box>
              <Tabs
                value={tabValue}
                indicatorColor="primary"
                textColor="primary"
                onChange={handleTabChange}>
                <Tab label={translate("TABS.ONBOARDING")} />
                <Tab
                  label={translate("TABS.ADVISORS")}
                  disabled={storeRecord.bulkPhase1 !== "Completed"}
                />
                <Tab
                  label={translate("TABS.TECHNICIANS")}
                  disabled={storeRecord.bulkPhase1 !== "Completed"}
                />
                <Tab
                  label={translate("TABS.LABOR_GRID")}
                  disabled={storeRecord.bulkPhase1 === "Inactive"}
                />
                <Tab
                  label={translate("TABS.PARTS_MATRIX")}
                  disabled={storeRecord.bulkPhase1 === "Inactive"}
                />
                <Tab
                  label={"Models"}
                  disabled={storeRecord.bulkPhase1 !== "Completed"}
                />
                <Tab
                  label={"Menu"}
                  disabled={storeRecord.bulkPhase1 !== "Completed"}
                />
                <Tab
                  label={translate("TABS.STORE_SETTINGS")}
                  disabled={storeRecord.bulkPhase1 !== "Completed"}
                />
                <Tab
                  label={"Revenue Details"}
                  disabled={storeRecord.bulkPhase1 !== "Completed"}
                />
                <Tab label={"Activity Log"} />
              </Tabs>
              <Divider />
              <TabPanel value={tabValue} index={0}>
                <OnBoarding getStoreQuery={getStoreQuery} />
              </TabPanel>
              <TabPanel value={tabValue} index={1}>
                <Advisors />
              </TabPanel>
              <TabPanel value={tabValue} index={2}>
                <Technicians />
              </TabPanel>
              <TabPanel value={tabValue} index={3}>
                <LaborGrid getStoreQuery={getStoreQuery} />
              </TabPanel>
              <TabPanel value={tabValue} index={4}>
                <PartsMatrix getStoreQuery={getStoreQuery} />
              </TabPanel>
              <TabPanel value={tabValue} index={5}>
                <Models />
              </TabPanel>
              <TabPanel value={tabValue} index={6}>
                <AvailableMenu />
              </TabPanel>
              <TabPanel value={tabValue} index={7}>
                <StoreSettings />
                {/* <StoreInfo /> */}
              </TabPanel>
              <TabPanel value={tabValue} index={8}>
                <RevenueStatement />
              </TabPanel>
              <TabPanel value={tabValue} index={9}>
                <ActivityLog spec={"storeSpecific"} />
              </TabPanel>
            </CardContent>
          </Card>
        </Box>
      </Box>
    </RecordContextProvider>
  );
};
