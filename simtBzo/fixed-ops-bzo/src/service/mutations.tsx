import FetchConfig from "./fetchConfig";
import { IEmailQueryInput, IFetchConfigReturn } from "../types";

const userEmailId = localStorage.getItem("userEmail");
const userRole = localStorage.getItem("role");
export const DeleteTenant = (
  pAction: string,
  tenantid: string,
  tenantname: string,
  inDeletedDate?: any
) => {
  console.log("vvvvvvv");
  const variables = {
    pAction: pAction,
    tenantid: tenantid,
    tenantname: tenantname,
    inDeletedDate: inDeletedDate,
    userid: localStorage.getItem("userEmail"),
  };
  return FetchConfig({
    anonymous: false,
    query: `mutation statelessServiceBzoInsertOrUpdateTenantMaster(
      $pAction: String!
      $tenantid: String!
      $tenantname: String!
      $userid: String!
      $inDeletedDate: Date
    ) {
      statelessServiceBzoInsertOrUpdateTenantMaster(
        input: {
          pAction: $pAction
          inDeletedDate: $inDeletedDate
          tenantid: $tenantid
          tenantname: $tenantname
          userid: $userid
        }
      ) {
        string
      }
    }`,
    variables: variables,
  }).then((response: IFetchConfigReturn) => {
    return response.data.statelessServiceBzoInsertOrUpdateTenantMaster;
  });
};

export const MutateDms = (pAction: string, pDms: string, pDmsImg: string) => {
  const variables = {
    pAction: pAction,
    pDms: pDms,
    pDmsImg: pDmsImg,
    userid: localStorage.getItem("userEmail"),
  };
  return FetchConfig({
    anonymous: false,
    query: `mutation statelessServiceBzoInsertOrUpdateDmsMaster(
      $pAction: String!
      $pDms: String!
      $pDmsImg: String!
      $userid: String!
    ) {
      statelessServiceBzoInsertOrUpdateDmsMaster(
        input: {
          pAction: $pAction
          pDms: $pDms
          pDmsImg: $pDmsImg
          userid: $userid
        }
      ) {
        string
      }
    }
  `,
    variables: variables,
  }).then((response: IFetchConfigReturn) => {
    return response.data.statelessServiceBzoInsertOrUpdateDmsMaster;
  });
};

export const MutateGroupingMaster = (
  pAction: string,
  groupdesc: string,
  groupid: number,
  storeid: string[],
  groupname: string,
  tenantId: string
) => {
  const variables = {
    pAction: pAction,
    groupdesc: groupdesc,
    groupid: groupid,
    storeid: storeid,
    groupname: groupname,
    inTenantId: tenantId,
    userid: localStorage.getItem("userEmail"),
  };
  return FetchConfig({
    anonymous: false,
    query: `mutation statelessServiceBzoInsertOrUpdateGroupingMaster(
      $pAction: String!
      $groupdesc: String!
      $groupid: Int
      $storeid: [String]
      $groupname: String!
      $userid: String!
      $inTenantId: String!
    ) {
      statelessServiceBzoInsertOrUpdateGroupingMaster(
        input: {
          pAction: $pAction
          groupdesc: $groupdesc
          groupid: $groupid
          storeid: $storeid
          groupname: $groupname
          userid: $userid
          inTenantId: $inTenantId
        }
      ) {
        string
      }
    }
  `,
    variables: variables,
  }).then((response: IFetchConfigReturn) => {
    return response.data.statelessServiceBzoInsertOrUpdateGroupingMaster;
  });
};

const InsertWorkPackage = (id: string) => {
  const variables = {
    inId: id,
  };
  return FetchConfig({
    anonymous: false,
    query: `mutation InsertWorkPackage($inId: String!) {
      statelessServiceBzoInsertOrUpdateWorkPackage(input: {inId: $inId}) {
        results {
          msg
          status
        }
      }
    }`,
    variables: variables,
  }).then((response: IFetchConfigReturn) => {
    return response.data.statelessServiceBzoInsertOrUpdateWorkPackage
      .results[0];
  });
};
const InsertWorkPackageHierarchies = (id: string) => {
  const variables = {
    inId: id,
  };
  return FetchConfig({
    anonymous: false,
    query: `mutation InsertWorkPackage($inId: String!) {
      statelessServiceBzoInsertOrUpdateWorkPackageHierarchies(input: {inId: $inId}) {
        results {
          msg
          status
        }
      }
    }`,
    variables: variables,
  }).then((response: IFetchConfigReturn) => {
    return response.data;
  });
};

// export const InsertOrUpdateTenant = (input: any) => {
//   console.log("iiii----", input);
//   const variables = {
//     pAction: input.pAction,
//     tenantid: input.tenantId,
//     tenantname: input.tenantName,
//     tenantdesc: input.tenantDesc,
//     subdomain: input.subDomain,
//     pTenantImg: input.tenantImg,
//     agreementDate: input.agreementDate,
//     userid: localStorage.getItem("userEmail"),
//   };
//   return FetchConfig({
//     anonymous: false,
//     query: `mutation statelessServiceBzoInsertOrUpdateTenantMaster(
//       $pAction: String!
//       $tenantid: String!
//       $tenantname: String!
//       $tenantdesc: String!
//       $subdomain: String
//       $pTenantImg: String
//       $agreementDate: Date
//       $userid: String!
//     ) {
//       statelessServiceBzoInsertOrUpdateTenantMaster(
//         input: {
//           pAction: $pAction
//           tenantid: $tenantid
//           tenantname: $tenantname
//           tenantdesc: $tenantdesc
//           subdomain: $subdomain
//           pTenantImg: $pTenantImg
//           pAgreementDate: $agreementDate
//           userid: $userid
//         }
//       ) {
//         string
//       }
//     }`,
//     variables: variables,
//   }).then((response: IFetchConfigReturn) => {
//     return response.data.statelessServiceBzoInsertOrUpdateTenantMaster;
//   });
// };
export const InsertOrUpdateTenant = (input: any) => {
  console.log("jjjjj----", input);

  const variables = {
    displayName: input.displayName,
    pAction: input.pAction,
    tenantid: input.tenantId,
    pTenantImg: input.tenantImg,
    pAgreementDate: input.agreementDate,
    userid: localStorage.getItem("userEmail"),
  };
  return FetchConfig({
    anonymous: false,
    query: `mutation statelessServiceBzoUpdateTenantMaster(
        $pAction: String!
        $displayName: String!
        $tenantid: String!
        $pTenantImg: String
        $pAgreementDate: Date
        $userid: String!
      ) {
        statelessServiceBzoUpdateTenantMaster(
          input: {
            pAction: $pAction
            pDisplayName: $displayName
            tenantid: $tenantid
            pTenantImg: $pTenantImg
            pAgreementDate: $pAgreementDate
            userid: $userid
          }
        ) {
          string
        }
      }`,
    variables: variables,
  }).then((response: IFetchConfigReturn) => {
    return response.data.statelessServiceBzoUpdateTenantMaster;
  });
};
export const OpcodeMutation = (
  pVal: JSON,
  pTenantId: string,
  pStoreId: string
) => {
  const variables = {
    pVal: JSON.stringify(pVal),
    pUserid: localStorage.getItem("userEmail"),
    pTenantId: pTenantId,
    pStoreId: pStoreId,
  };
  return FetchConfig({
    anonymous: false,
    query: `mutation statelessServiceBzoInsertOrUpdateOnboardingOpcodeCategorization(
      $pVal: JSON!
      $pUserid: String!
      $pTenantId: String!
      $pStoreId: String!
    ) {
      statelessServiceBzoInsertOrUpdateOnboardingOpcodeCategorization(
        input: {
          pVal: $pVal
          pUserid: $pUserid
          pTenantId: $pTenantId
          pStoreId: $pStoreId
        }
      ) {
        string
      }
    }`,
    variables: variables,
  }).then((response: IFetchConfigReturn) => {
    return response.data
      .statelessServiceBzoInsertOrUpdateOnboardingOpcodeCategorization;
  });
};

// export const InsertStore = (input: any) => {
//   alert(56);
//   const variables = {
//     pDms: input.dms,
//     storename: input.storeName.trim(),
//     storedesc: input.storeDesc,
//     storeid: input.storeId,
//     tenantid: input.tenantId,
//     dealeraddress: input.dealerAddress,
//     pManufacturer: input.manufacturer,
//     pWebsite: input.website,
//     sortorder: input.sortOrder,
//     companyNumber: input.companyNumber,
//     dealerId: input.dealerId,
//     enterpriseCode: input.enterpriseCode,
//     serverName: input.serverName,
//     stateCode: input.stateCode,
//     storelauncheddate: input.storeLaunchedDate,
//     billingdate: input.billingDate,
//     inSfStoreId: input.inSfStoreId,
//     inSubscriptionId: null,
//     inRealmName: input.realmName,
//     userid: "",
//   };
//   return FetchConfig({
//     anonymous: false,
//     query: `mutation InsertStore(
//   $pDms: String!
//   $storename: String!
//   $storedesc: String!
//   $storeid: String!
//   $tenantid: String!
//   $dealeraddress: String!
//   $pManufacturer: String!
//   $pWebsite: String!
//   $sortorder: Int!
//   $companyNumber: String
//   $dealerId: String
//   $enterpriseCode: String
//   $serverName: String
//   $stateCode: String
//   $storelauncheddate: Date
//   $billingdate: Date
//   $inSfStoreId: String
//   $inSubscriptionId: String
//   $inRealmName: String
//   $userid: String!
// ) {
//   statelessServiceBzoInsertOrUpdateStoreMaster(
//     input: {
//       pDms: $pDms
//       storename: $storename
//       storedesc: $storedesc
//       storeid: $storeid
//       tenantid: $tenantid
//       pManufacturer: $pManufacturer
//       sortorder: $sortorder
//       dealeraddress: $dealeraddress
//       pWebsite: $pWebsite
//       companynumber: $companyNumber
//       dealerid: $dealerId
//       enterprisecode: $enterpriseCode
//       servername: $serverName
//       statecode: $stateCode
//       storelauncheddate: $storelauncheddate
//       billingdate: $billingdate
//       inSfStoreId: $inSfStoreId
//       inSubscriptionId: $inSubscriptionId
//       inRealmName: $inRealmName
//       userid: $userid
//     }
//   ) {
//     string
//   }
// }
//     `,
//     variables: variables,
//   }).then((response: IFetchConfigReturn) => {
//     return response.data.statelessServiceBzoInsertOrUpdateStoreMaster.string;
//   });
// };

export const InsertStore = (input: any) => {
  const variables = {
    pDms: input.dms,
    storename: input.storeName?.trim(),
    storedesc: input.storeDesc,
    storeid: input.storeId,
    tenantid: input.tenantId,
    dealeraddress: input.dealerAddress,
    pManufacturer: input.manufacturer,
    pWebsite: input.website,
    sortorder: Number(input.sortOrder) || null, // ensure Int
    companynumber: input.companyNumber || null,
    dealerid: input.dealerId || null,
    enterprisecode: input.enterpriseCode || null,
    servername: input.serverName || null,
    statecode: input.stateCode || null,
    storelauncheddate: input.storeLaunchedDate || null, // Date or null
    billingdate: input.billingDate || null, // Date or null
    inSfStoreId: input.inSfStoreId || null,
    inSubscriptionId: null,
    inRealmName: input.realmName || null,
    userid: "", // required by mutation
    displayName: input.displayName,
  };

  return FetchConfig({
    anonymous: false,
    query: `mutation InsertStore(
      $pDms: String!
      $storename: String!
      $storedesc: String!
      $storeid: String!
      $tenantid: String!
      $dealeraddress: String!
      $pManufacturer: String!
      $pWebsite: String!
      $sortorder: Int
      $companynumber: String
      $dealerid: String
      $enterprisecode: String
      $servername: String
      $statecode: String
      $storelauncheddate: Date
      $billingdate: Date
      $inSfStoreId: String
      $inSubscriptionId: String
      $inRealmName: String
      $userid: String!
      $displayName: String
    ) {
      statelessServiceBzoInsertOrUpdateStoreMaster(
        input: {
          pDms: $pDms
          storename: $storename
          storedesc: $storedesc
          storeid: $storeid
          tenantid: $tenantid
          pManufacturer: $pManufacturer
          sortorder: $sortorder
          dealeraddress: $dealeraddress
          pWebsite: $pWebsite
          companynumber: $companynumber
          dealerid: $dealerid
          enterprisecode: $enterprisecode
          servername: $servername
          statecode: $statecode
          storelauncheddate: $storelauncheddate
          billingdate: $billingdate
          inSfStoreId: $inSfStoreId
          inSubscriptionId: $inSubscriptionId
          inRealmName: $inRealmName
          userid: $userid
          pDisplayName: $displayName
        }
      ) {
        string
      }
    }`,
    variables: variables,
  })
    .then((response: IFetchConfigReturn) => {
      return response.data.statelessServiceBzoInsertOrUpdateStoreMaster.string;
    })
    .catch((err) => {
      console.error("GraphQL Error:", err);
      throw err; // rethrow if needed
    });
};

export const InsertUpdateMultipleDmsDetails = (input: any) => {
  const variables = {
    pDms: input.dms,
    storename: input.storeName.trim(),
    storedesc: input.storeDesc,
    storeid: input.storeId,
    tenantid: input.tenantId,
    dealeraddress: input.dealerAddress,
    pManufacturer: input.manufacturer,
    pWebsite: input.website,
    sortorder: input.sortOrder,
    userid: localStorage.getItem("userEmail"),
    companyNumber: input.companyNumber,
    dealerId: input.dealerId,
    enterpriseCode: input.enterpriseCode,
    serverName: input.serverName,
    stateCode: input.stateCode,
    storelauncheddate: input.storeLaunchedDate,
    billingdate: input.billingDate,
    inSfStoreId: input.inSfStoreId,
    inSubscriptionId: null,
    inEffectiveDate: input.effectiveDate,
    pAction: input.action,
    inRealmName: input.realmName,
    sId: input.action == "insert" ? null : input.id,
  };
  return FetchConfig({
    anonymous: false,
    query: `mutation InsertUpdateMultipleDmsDetails(
  $pDms: String!
  $storename: String!
  $storedesc: String
  $storeid: String!
  $tenantid: String!
  $dealeraddress: String
  $pManufacturer: String
  $pWebsite: String
  $sortorder: Int!
  $userid: String!
  $companyNumber: String
  $dealerId: String
  $enterpriseCode: String
  $serverName: String
  $stateCode: String
  $storelauncheddate: Date
  $billingdate: Date
  $inSfStoreId: String
  $inSubscriptionId: String
  $inEffectiveDate: Date
  $pAction: String!,
    $sId: Int,
    $inRealmName: String
) {
  statelessServiceBzoInsertOrUpdateStoreWithMultipleDms(
    input: {
      pDms: $pDms
      storename: $storename
      storedesc: $storedesc
      storeid: $storeid
      tenantid: $tenantid
      userid: $userid
      pManufacturer: $pManufacturer
      sortorder: $sortorder
      dealeraddress: $dealeraddress
      pWebsite: $pWebsite
      companynumber: $companyNumber
      dealerid: $dealerId
      enterprisecode: $enterpriseCode
      servername: $serverName
      statecode: $stateCode
      storelauncheddate: $storelauncheddate
      billingdate: $billingdate
      inSfStoreId: $inSfStoreId
      inSubscriptionId: $inSubscriptionId
      inEffectiveDate: $inEffectiveDate
      pAction: $pAction,
      sId: $sId,
      inRealmName: $inRealmName
    }
  ) {
    results{
      statusCode
      statusMsg
    }
  }
}
    `,
    variables: variables,
  }).then((response: IFetchConfigReturn) => {
    return response.data.statelessServiceBzoInsertOrUpdateStoreWithMultipleDms
      .results[0];
  });
};

export const StoreOnboardingEmail = (input: IEmailQueryInput) => {
  const variables = {
    pDms: input.pDms,
    pNewStore: input.pNewStore,
    pStore: input.pStore,
    pStoreId: input.pStoreId,
    pTenant: input.pTenant,
    pTenantId: input.pTenantId,
    pUserid: localStorage.getItem("userEmail"),
  };
  return FetchConfig({
    anonymous: false,
    query: `mutation statelessServiceBzoStoreOnboardingEmail ($pDms: String!, $pNewStore: Int!, $pStore: String!, $pStoreId: String!, $pTenant: String,$pTenantId: String, $pUserid: String!) {
      statelessServiceBzoStoreOnboardingEmail(
        input: {pDms: $pDms, pNewStore: $pNewStore, pStore: $pStore,pStoreId: $pStoreId, pTenant: $pTenant,pTenantId: $pTenantId, pUserid: $pUserid}
      ) {
        string
      }
    }
    `,
    variables: variables,
  }).then((response: IFetchConfigReturn) => {
    return response;
  });
};

export const SetChecklistStatus = (input: any) => {
  const variables = {
    pProcess: input.pProcess,
    pStatus: input.pStatus,
    pChecklist: input.pChecklist,
    pStore: input.pStore,
    pTenant: input.pTenant,
    pUserid: localStorage.getItem("userEmail"),
  };
  return FetchConfig({
    anonymous: false,
    query: `mutation statelessServiceBzoSetOnboardingChecklist($pProcess: String!, $pTenant: String!, $pStore: String!, $pUserid: String!, $pChecklist: String!, $pStatus: String!) {
      statelessServiceBzoSetOnboardingChecklist(
        input: {pProcess: $pProcess, pTenant:$pTenant, pStore: $pStore, pUserid: $pUserid, pChecklist: $pChecklist, pStatus: $pStatus}
      ) {
        clientMutationId
        string
      }
    }
    `,
    variables: variables,
  }).then((response: IFetchConfigReturn) => {
    return response.data.statelessServiceBzoSetOnboardingChecklist.string;
  });
};

const UpdateAdvisorDetails = (input: any) => {
  const variables = {
    advisor: input.serviceadvisor,
    pCategorized: input.active == null ? 0 : 1,
    statusval: input.active,
    nickName: input.nickname,
    userId: localStorage.getItem("userEmail"),
    inStoreId: input.storeId,
    inTenantId: input.tenantId,
    userRole: userRole,
    pDepartment: null,
  };
  return FetchConfig({
    anonymous: false,
    storeid: input.storeId,
    query: `mutation UpdateAdvisorDetails(
      $advisor: String
      $pCategorized: Int
      $statusval: BitString
      $nickName: String
       $inStoreId: String
       $inTenantId: String
       $userRole: String
       $pDepartment: String
       $userId: String
    ) {
      statelessServiceBzoUpdateServiceAdvisorStatus(
        input: {
          advisor: $advisor
          statusval: $statusval
          pCategorized: $pCategorized
          nickName: $nickName
          inStoreId: $inStoreId
          inTenantId: $inTenantId
          userRole: $userRole
          pDepartment: $pDepartment,
          userId: $userId
        }
      ) {
        clientMutationId
      }
    }
  `,
    variables: variables,
  }).then((response: IFetchConfigReturn) => {
    return response;
  });
};

const GetSetStoreSettings = (input: any) => {
  const variables = {
    getorset: input.action,
    pGoal: input.pGoal == null ? "null" : input.pGoal,
    settingtype: input.settingtype,
    timezoneValue: input.timezone,
    userid: localStorage.getItem("userEmail"),
    storeNickName: input.storeNickName ? input.storeNickName : "",
    inTenantId: input.tenantId,
    inStoreId: input.storeId,
  };
  return FetchConfig({
    anonymous: false,
    storeid: input.storeId,
    query: `mutation SetStoreSetting(
      $getorset: String
      $pGoal: JSON
      $settingtype: String
      $timezoneValue: String
      $userid: String
      $storeNickName: String
      $inTenantId: String
      $inStoreId: String
    ) {
      statelessServiceBzoGetorsetStoreSettings(
        input: {
          getorset: $getorset
          pGoal: $pGoal
          settingtype: $settingtype
          timezoneValue: $timezoneValue
          userid: $userid
          storeNickName: $storeNickName
          inTenantId: $inTenantId
          inStoreId: $inStoreId
        }
      ) {
        results {
          keyname
          keyvalue
          active
        }
      }
    }
  `,
    variables: variables,
  }).then((response: IFetchConfigReturn) => {
    return response.data.statelessServiceBzoGetorsetStoreSettings.results;
  });
};

export const updateTechnicianStatus = (
  input: any,
  storeId: any,
  Realm: any
) => {
  const variables = {
    techno: input.techno,
    pCategorized: input.pCategorized,
    statusval: input.statusval,
    nickName: input.nickName,
    userId: localStorage.getItem("userEmail"),
    inTenantId: input.tenantId,
    inStoreId: input.storeId,
    pDepartment: "",
    userRole: userRole,
  };
  return FetchConfig({
    anonymous: false,
    storeid: storeId,
    query: `mutation updateTechnicianStatus(
      $techno: String
      $pCategorized: Int
      $statusval: BitString
      $nickName: String
      $userId: String
      $inTenantId: String
      $inStoreId: String
      $userRole: String
      $pDepartment: String

    ) {
      statelessServiceBzoUpdateTechnicianStatus(
        input: {
          techno: $techno
          statusval: $statusval
          pCategorized: $pCategorized
          nickName: $nickName
          userId: $userId
          inTenantId: $inTenantId
          inStoreId: $inStoreId,
          pDepartment: $pDepartment
          userRole: $userRole
        }
      ) {
        clientMutationId
      }
    }
  `,
    variables: variables,
  }).then((response: IFetchConfigReturn) => {
    return response.data.statelessServiceBzoUpdateTechnicianStatus;
  });
};

export const updatePayTypeMasterByCode = (
  pVal: JSON,
  storeId: any,
  tenantId: any
) => {
  const variables = {
    inPPaytype: JSON.stringify(pVal),
    inStoreId: storeId,
    inTenantId: tenantId,
    inUserId: localStorage.getItem("userEmail"),
  };
  return FetchConfig({
    anonymous: false,
    storeid: storeId,
    query: `mutation updatePayTypeMasterByCode(
      $inPPaytype: JSON
      $inStoreId: String
      $inUserId: String
      $inTenantId: String
    ) {
       statelessServiceBzoUpdatePaytypeMasterByPaytypecode(
        input: { inPPaytype: $inPPaytype, inStoreId: $inStoreId, inUserId: $inUserId,  inTenantId: $inTenantId}
      ) {
       
          statuses
        
      }
    }
  `,
    variables: variables,
  }).then((response: IFetchConfigReturn) => {
    return response.data.statelessServiceBzoUpdatePaytypeMasterByPaytypecode;
  });
};

const ConfigureTenant = (id: any) => {
  const variables = {
    tenantMasterId: id,
  };
  return FetchConfig({
    anonymous: false,
    query: `mutation ConfigureTenant($tenantMasterId: Int!) {
      createTenant(input: {tenantMasterId: $tenantMasterId}) {
        json
        clientMutationId
      }
    }
  `,
    variables: variables,
  }).then((response: IFetchConfigReturn) => {
    return JSON.parse(response.data.createTenant.json);
  });
};

const ConfigureStore = (id: any) => {
  const variables = {
    storeMasterId: id,
  };
  return FetchConfig({
    anonymous: false,
    query: `mutation ConfigureStore($storeMasterId: Int!) {
      createStore(input: {storeMasterId: $storeMasterId}) {
        json
        clientMutationId
      }
    }
  `,
    variables: variables,
  }).then((response: IFetchConfigReturn) => {
    return JSON.parse(response.data.createStore.json);
  });
};

export const InsertOrUpdatePartsMatrix = (
  realm: string,
  pCall: string,
  pStore: string,
  pNewPrtsource: string,
  pOldPrtsource: string,
  pMatrixType: string,
  pMatrixOrFleet: string,
  pCreatedDate: string,
  pStoreInstallDate: string,
  pMatrix: JSON,
  inTenantId: String
) => {
  const variables = {
    realm: realm,
    pCall: pCall,
    pStore: pStore,
    pMatrixOrFleet: pMatrixOrFleet,
    pNewPrtsource: pNewPrtsource,
    pOldPrtsource: pOldPrtsource,
    pMatrixType: pMatrixType,
    pCreatedDate: pCreatedDate,
    pStoreInstallDate: pStoreInstallDate,
    pMatrix: pMatrix ? JSON.stringify(pMatrix) : null,
    pUserId: localStorage.getItem("userEmail"),
    inTenantId: inTenantId,
  };
  return FetchConfig({
    anonymous: false,
    storeid: pStore,
    query: `mutation  InsertOrUpdatePartsMatrix( 
      $pCall: String!
      $pStore: String!
      $pMatrixOrFleet: String!
      $pNewPrtsource: [String!]!
      $pOldPrtsource: [String!]!
      $pMatrixType: String
      $pCreatedDate: Date!
      $pStoreInstallDate: Date!
      $pMatrix: JSON
      $pUserId: String!
      $inTenantId: String!
      
    ) {
      statelessServiceBzoInsertOrUpdateKpiScorecardPartsMatrix(
        input: {
          pCall: $pCall,
          pStore: $pStore,
          pMatrixOrFleet: $pMatrixOrFleet,
          pNewPrtsource: $pNewPrtsource,
          pOldPrtsource: $pOldPrtsource,
          pMatrixType: $pMatrixType,
          pCreatedDate: $pCreatedDate,
          pStoreInstallDate: $pStoreInstallDate,
          pMatrix: $pMatrix,
          pUserId: $pUserId,
          inTenantId:$inTenantId
        }
      ) {
        results {
          msg
          rStatus
        }
      }

    }`,
    variables: variables,
  }).then((response: IFetchConfigReturn) => {
    return response.data
      .statelessServiceBzoInsertOrUpdateKpiScorecardPartsMatrix;
  });
};

export const fetchKpiScorecardPartsMatrix = (input: any) => {
  const variables = {
    pCallType: input.pCallType,
    pStore: input.pStore,
    inTenantId: input.tenantId,
  };
  return FetchConfig({
    anonymous: false,
    storeid: input.pStore,
    query: `
      query MyQuery(
        $pCallType: String!
        $pStore: String!
        $inTenantId: String!
      ) {
        statelessServiceBzoGetKpiScorecardPartsMatrix(
          pStore: $pStore
          pCallType: $pCallType
          inTenantId: $inTenantId
        ) {
          nodes {
            partsFor
            priceStartRange
            priceEndRange
            addPercentage
            prtsource
            createdDate
            matrixType
            storeInstallDate
            calcBase
            breakField
            matrixOrder
            prtsourceList
          }
        }
      }
    `,
    variables: variables,
  }).then((response) => {
    return response.data.statelessServiceBzoGetKpiScorecardPartsMatrix.nodes;
  });
};

export const fetchKpiScorecardPartsMatrixDetails = (input: any) => {
  //ToDo: Need to move this to dataFetchQueries Page
  const variables = {
    pMatrixType: input.pMatrixType,
    pCreatedDate: input.pCreatedDate,
    pStore: input.pStore,
    pCallType: input.pCallType,
    pPrtsource: input.pPrtsource,
    inTenantId: input.tenantId,
  };

  return FetchConfig({
    anonymous: false,
    Realm: input.realm,
    storeid: input.pStore,
    query: `
      query MyQuery(
        $pCallType: String!
        $pStore: String!
        $pMatrixType: String!
        $pCreatedDate: Date!
        $pPrtsource: [String!]!
        $inTenantId: String!
      ) {
        statelessServiceBzoGetKpiScorecardPartsMatrix(
          pCallType: $pCallType
          pStore: $pStore
          pMatrixType: $pMatrixType
          pCreatedDate: $pCreatedDate
          pPrtsource:$pPrtsource
           inTenantId: $inTenantId
        ) {
          edges {
            node {
            partsFor
            priceStartRange
            priceEndRange
            addPercentage
            prtsource
            createdDate
            matrixType
            storeInstallDate
            calcBase
            breakField
            }
          }
        }
      }
    `,
    variables: variables,
  }).then((response) => {
    return response.data.statelessServiceBzoGetKpiScorecardPartsMatrix.edges.map(
      (edge: any) => edge.node
    );
  });
};

const EnableDisableTenant = (id: number, status: string) => {
  const variables = {
    tenantMasterId: id,
  };
  return FetchConfig({
    anonymous: false,
    query:
      status === "Enabled"
        ? `mutation DisableRealm($tenantMasterId: Int!) {
      disableRealm(input: {tenantMasterId: $tenantMasterId}) {
        clientMutationId
        string
      }
    }
  `
        : `mutation EnableRealm($tenantMasterId: Int!) {
    enableRealm(input: {tenantMasterId: $tenantMasterId}) {
      clientMutationId
      string
    }
  } 
`,
    variables: variables,
  }).then((response: IFetchConfigReturn) => {
    return response.data;
  });
};

const InsertUpdateLaborGrid = (input: any) => {
  let API =
    input.pCall === "insert"
      ? "statelessServiceBzoInsertOrUpdateKpiScorecardLaborGridMultiple"
      : "statelessServiceBzoInsertOrUpdateKpiScorecardLaborGrid";
  let newGridType =
    input.pCall === "insert"
      ? { pNewGridTypes: input.pNewGridType }
      : { pNewGridType: input.pNewGridType };
  const variables = {
    ...newGridType,
    pStore: input.storeId,
    inTenantId: input.tenantId,
    pOldGridType: input.pCall === "insert" ? "" : input.pOldGridType,
    pCreatedDate: input.createdDate,
    pStoreInstallDate: input.storeInstallDate,
    pGrid: input.gridData ? JSON.stringify(input.gridData) : input.gridData,
    pUserId: localStorage.getItem("userEmail"),
    pCall: input.pCall,
    pLaborMissType: input.pLaborMissType,
    pGridFor: input.pGridFor,
    pConfirmFlag: input.pConfirmFlag,
    pIsDefault: input.pIsDefault,
  };
  return FetchConfig({
    anonymous: false,
    storeid: input.storeId,
    query: `mutation InsertUpdateLaborGrid($pStore: String!
      $inTenantId: String!
      $pOldGridType: String!
      ${
        input.pCall === "insert"
          ? "$pNewGridTypes: [String]!"
          : "$pNewGridType: String!"
      }
      $pGridFor: String!
      $pCreatedDate: Date!
      $pStoreInstallDate: Date!
      $pGrid: JSON
      $pUserId: String!
      $pCall: String!
      $pLaborMissType: String!
      $pConfirmFlag: String!
      $pIsDefault: String!
      ) {
        ${API}(input: {pStore: $pStore,
          inTenantId: $inTenantId
        ${
          input.pCall === "insert"
            ? "pNewGridTypes: $pNewGridTypes"
            : "pNewGridType: $pNewGridType"
        },
          pOldGridType: $pOldGridType,
          pGridFor: $pGridFor,
        pCreatedDate: $pCreatedDate,
        pStoreInstallDate: $pStoreInstallDate,
        pGrid: $pGrid,
        pCall: $pCall,
        pLaborMissType: $pLaborMissType,
        pConfirmFlag: $pConfirmFlag,
        pIsDefault: $pIsDefault,
        pUserId: $pUserId}) {
          clientMutationId
           ${
             input.pCall === "insert"
               ? "json"
               : `results{
            msg
            rStatus
          }`
           }

       }
    }
  `,
    variables: variables,
  }).then((response: IFetchConfigReturn) => {
    let json =
      input.pCall === "insert"
        ? response.data
            .statelessServiceBzoInsertOrUpdateKpiScorecardLaborGridMultiple.json
        : "";
    return input.pCall === "insert"
      ? JSON.parse(json)
      : response.data.statelessServiceBzoInsertOrUpdateKpiScorecardLaborGrid
          .results[0];
  });
};

const InsertGriddataDtl = (input: any) => {
  const variables = {
    inCreatedDate: input.inCreatedDate,
    inGridTypes: input.inGridType,
    inStoreId: input.inStoreId,
    inDoorRate: input.inDoorRate,
    inGridFor: input.inGridFor,
    inTenantId: input.inTenantId,
  };
  return FetchConfig({
    anonymous: false,
    storeid: input.storeId,
    query: `mutation InsertGriddataDtl(
      $inCreatedDate: Date!
      $inGridTypes: [String]
      $inStoreId: String!
      $inDoorRate:BigFloat!
      $inGridFor: String!
      $inTenantId: String!,
      ) {
        statelessServiceBzoInsertGriddataDtlMultiple(input: {inCreatedDate: $inCreatedDate,
          inGridFor: $inGridFor,
          inGridTypes: $inGridTypes,
          inStoreId: $inStoreId,
          inDoorRate:$inDoorRate, inTenantId: $inTenantId,}) {
            clientMutationId
            json
       }
    }
  `,
    variables: variables,
  }).then((response: IFetchConfigReturn) => {
    let json = response.data.statelessServiceBzoInsertGriddataDtlMultiple.json;
    return JSON.parse(json);
  });
};

export const FileUpload = (input: any) => {
  const variables = {
    base64Data: input.base64Data,
    inFileName: input.inFileName,
    inGridTypes: input.inGridType,
    inInstallationDate: input.inInstallationDate,
    inStoreId: input.inStoreId,
    inTenantId: input.inTenantId,
    inCreatedUser: localStorage.getItem("userEmail"),
    inGridOrFleet: input.inGridOrFleet,
  };
  return FetchConfig({
    anonymous: false,
    Realm: input.realm,
    storeid: input.storeId,
    query: `mutation FileUpload(
      $base64Data: String!
      $inFileName: String!
      $inGridTypes: [String]
      $inInstallationDate:Date
      $inStoreId: String!
      $inTenantId: String!
      $inCreatedUser:String!
      $inGridOrFleet: String!
      ) {
        statelessServiceBzoInsertGriddataDtlFileUploadLogMultiple(input: {base64Data: $base64Data,
          inFileName: $inFileName,
          inGridTypes: $inGridTypes,
          inInstallationDate:$inInstallationDate,
          inStoreId: $inStoreId,
          inTenantId: $inTenantId,
          inCreatedUser:$inCreatedUser
          inGridOrFleet:$inGridOrFleet
          }) {
            clientMutationId
            json
       }
    }
  `,
    variables: variables,
  }).then((response: IFetchConfigReturn) => {
    let json =
      response.data.statelessServiceBzoInsertGriddataDtlFileUploadLogMultiple
        .json;
    return JSON.parse(json);
  });
};

const UpdateBulkStatus = (input: any) => {
  const variables = {
    inCallType: input.pCallType,
    inTenantId: input.pTenantId,
    inStoreId: input.pStoreId,
    status: input.status,
  };
  return FetchConfig({
    anonymous: false,
    Realm: String(process.env.REACT_APP_KEYCLOAK_REALM),
    storeid: input.storeId,
    query: `mutation updateBulkStatus ($inCallType: String!,
      $inTenantId: String!,
      $inStoreId: String!,
      $status:String!){
      statelessServiceBzoUpdateBulkLoadStatus(
        input: {inCallType: $inCallType, inStoreId: $inStoreId, inTenantId: $inTenantId, status: $status}
      ) {
        clientMutationId
        string
      }
    }
  `,
    variables: variables,
  }).then((response: IFetchConfigReturn) => {
    return response.data.statelessServiceBzoUpdateBulkLoadStatus.string;
  });
};

export const PartsMatrixFileUpload = (input: any) => {
  const variables = {
    base64Data: input.base64Data,
    inFileName: input.inFileName,
    inMatrixType: input.inMatrixType,
    inInstallationDate: input.inInstallationDate,
    inStoreId: input.inStoreId,
    inTenantId: input.inTenantId,
    inPrtsource: input.inPrtsource,
    inCreatedUser: localStorage.getItem("userEmail"),
    inMatrixOrFleet: input.inMatrixOrFleet,
  };
  return FetchConfig({
    anonymous: false,
    storeid: input.inStoreId,
    query: `mutation PartsMatrixFileUpload(
      $base64Data: String!
      $inFileName: String!
      $inMatrixType: String
      $inInstallationDate:Date
      $inStoreId: String!
      $inTenantId: String!
      $inPrtsource: [String!]!
      $inCreatedUser:String!
      $inMatrixOrFleet: String!
      ) {
        statelessServiceBzoInsertGriddataDtlPartsMatrixFileUploadLog(input: {base64Data: $base64Data,
          inFileName: $inFileName,
          inMatrixType: $inMatrixType,
          inInstallationDate:$inInstallationDate,
          inMatrixOrFleet: $inMatrixOrFleet,
          inStoreId: $inStoreId,
          inTenantId: $inTenantId,
          inPrtsource:$inPrtsource,
          inCreatedUser:$inCreatedUser}) {
            results {
              msg
              status
            }

       }
    }
  `,
    variables: variables,
  }).then((response: IFetchConfigReturn) => {
    return response.data
      .statelessServiceBzoInsertGriddataDtlPartsMatrixFileUploadLog;
  });
};

export const UpdateSubmitStatus = (
  inTenantId: any,
  inStoreId: any,
  inType: string
) => {
  const variables = {
    inStoreId: inStoreId,
    inTenantId: inTenantId,
    inType: inType,
  };
  return FetchConfig({
    anonymous: false,
    query: `mutation UpdateSubmitStatus(
      $inStoreId: String!
      $inTenantId:String!
      $inType: String!
      ) {
        statelessServiceBzoUpdateSubmissionStatus(input: {
          inStoreId: $inStoreId,
          inTenantId:$inTenantId,
          inType: $inType
        }) {
            clientMutationId
            results {
              msg
              status
            }
       }
    }
  `,
    variables: variables,
  }).then((response: IFetchConfigReturn) => {
    return response.data.statelessServiceBzoUpdateSubmissionStatus;
  });
};

export const getOpcodeCategoryStatus = (inTenantId: any, inStoreId: any) => {
  const variables = {
    inStoreId: inStoreId,
    inTenantId: inTenantId,
  };
  return FetchConfig({
    anonymous: false,
    query: `mutation updateOpcodeCategorizationStatus(
      $inStoreId: String!
      $inTenantId:String!
      ) {
        statelessServiceBzoGetOpcodeCategorizationStatus(input: {
          inStoreId: $inStoreId,
          inTenantId:$inTenantId}) {
           
    string
  
       }
    }
  `,
    variables: variables,
  }).then((response: IFetchConfigReturn) => {
    return response.data.statelessServiceBzoGetOpcodeCategorizationStatus;
  });
};

export const updateLaunchReport = (inTenantId: any, inStoreId: any) => {
  const variables = {
    inStoreId: inStoreId,
    inTenantId: inTenantId,
  };
  return FetchConfig({
    anonymous: false,
    query: `mutation MyMutation {
      statelessServiceBzoInsertOrUpdateWpCustomValues(
        input: {inStoreId: "", inCustomValues: "", inCallType: "Update"}
      ) {
        results {
          msg
          status
        }
      }
    }
  `,
    variables: variables,
  }).then((response: IFetchConfigReturn) => {
    return response.data.statelessServiceBzoGetOpcodeCategorizationStatus;
  });
};

export const updateMatrixType = (input: any) => {
  const variables = {
    inCreatedUser: localStorage.getItem("userEmail"),
    inOldMatrixType: input.inOldMatrixType,
    inNewMatrixType: input.inNewMatrixType,
    inStoreId: input.inStoreId,
    inTenantId: input.inTenantId,
    inActivity: input.inActivity,
  };
  return FetchConfig({
    anonymous: false,
    query: `mutation updateMatrixType(
      $inCreatedUser: String!
      $inOldMatrixType: String!
      $inStoreId: String!
      $inTenantId: String!
      $inNewMatrixType: String!,
      $inActivity: String!
    ) {
      statelessServiceBzoInsertPartsMatrixTypeMasterDetails(
        input: {
          inCreatedUser: $inCreatedUser,
          inOldMatrixType: $inOldMatrixType,
          inNewMatrixType: $inNewMatrixType,
          inStoreId: $inStoreId,
          inTenantId: $inTenantId,
          inActivity: $inActivity,
        }
      ) {
        results {
          msg
          status
        }
      }
    }`,
    variables: variables,
  }).then((response: IFetchConfigReturn) => {
    return response.data.statelessServiceBzoInsertPartsMatrixTypeMasterDetails
      .results[0];
  });
};

export const GetLaborMissesGridTypes = (record: any) => {
  const variables = {
    newGridtype: null,
    oldGridtype: null,
    pProcess: "get",
    userid: localStorage.getItem("userEmail"),
    inTenantId: record.tenantId,
    inStoreId: record.storeId,
  };
  return FetchConfig({
    anonymous: false,
    storeid: record.storeId,
    query: `mutation getLaborGridTypes($userid: String, $newGridtype: String, $oldGridtype: String, $pProcess: String, $inTenantId: String, $inStoreId: String) {
         statelessServiceBzoGetLaborMissesGridTypeMaster (input: {pProcess: $pProcess, oldGridtype: $oldGridtype, newGridtype: $newGridtype, userId: $userid, inTenantId: $inTenantId, inStoreId: $inStoreId}) {
                clientMutationId
                    laborMissesGridTypes {
                            gridType
                                }
                            }
                        }
                    `,
    variables: variables,
  }).then((response: IFetchConfigReturn) => {
    return response.data.statelessServiceBzoGetLaborMissesGridTypeMaster
      .laborMissesGridTypes;
  });
};

export const GetLaborMissesModels = (record: any) => {
  const variables = {
    userId: localStorage.getItem("userEmail"),
    inTenantId: record.tenantId,
    inStoreId: record.storeId,
  };
  return FetchConfig({
    anonymous: false,
    storeid: record.storeId,
    query: `mutation getLaborMissesModels($userId: String, $inTenantId: String, $inStoreId: String) {
          statelessServiceBzoGetLaborMissesModels(input: {userId: $userId, inTenantId: $inTenantId, inStoreId: $inStoreId}) {
                laborMissesModels {
                        gridType
                        make
                        model
                        categorized
                    }
                __typename
            }
        }                                   
      `,
    variables: variables,
  }).then((response: IFetchConfigReturn) => {
    return response.data.statelessServiceBzoGetLaborMissesModels
      .laborMissesModels;
  });
};

export const insertGridModelMapping = (pVal: any, record: any) => {
  const variables = {
    pVal: pVal,
    userId: localStorage.getItem("userEmail"),
    inTenantId: record.tenantId,
    inStoreId: record.storeId,
  };
  return FetchConfig({
    anonymous: false,
    storeid: record.storeId,
    Realm: record.realmName,
    query: `mutation insertMakeDetails($userId: String, $pVal: JSON, $inTenantId: String, $inStoreId: String) {
        statelessServiceBzoLaborMissesModelMapping(input: {userId: $userId, pVal: $pVal, inTenantId: $inTenantId, inStoreId: $inStoreId}) {
              string
              __typename
            }
          }
        `,
    variables: variables,
  }).then((response: IFetchConfigReturn) => {
    return response.data.statelessServiceBzoLaborMissesModelMapping;
  });
};

export const updateModels = (
  pVal: any,
  realmName: any,
  storeId: any,
  tenantId: any
) => {
  const variables = {
    userid: localStorage.getItem("userEmail"),
    pVal: pVal,
    inStoreId: storeId,
    inTenantId: tenantId,
  };
  return FetchConfig({
    anonymous: false,
    Realm: realmName,
    query: `mutation crudMenuModels($pVal: JSON!, $userid: String, $inStoreId: String, $inTenantId: String) {
      statelessServiceBzoCrudMenuModels(
        input: { pVal: $pVal, userid: $userid, inStoreId: $inStoreId, inTenantId: $inTenantId }
      ) {
        string
      }
    }
  `,
    variables: variables,
  }).then((response: IFetchConfigReturn) => {
    return response.data.statelessServiceBzoCrudMenuModels;
  });
};
export const DeleteSingleStore = (
  storeId: any,
  tenantId: any,
  inDeletedDate?: any
) => {
  const variables = {
    storeid: storeId,
    tenantid: tenantId,
    inDeletedDate: inDeletedDate,
  };
  return FetchConfig({
    anonymous: false,
    // Realm: realmName,
    storeid: storeId,
    query: `mutation DeleteSingleStore($storeid: String!, $tenantid: String!, $inDeletedDate: Date) {
      statelessServiceBzoDeleteStore(input: {storeid: $storeid, tenantid: $tenantid, inDeletedDate: $inDeletedDate}) {
        clientMutationId
        string
      }
    }
  `,
    variables: variables,
  }).then((response: IFetchConfigReturn) => {
    return response.data.statelessServiceBzoDeleteStore.string;
  });
};

export const addMenu = (input: any, realmName: any) => {
  console.log("yyyyyyyy--", input);
  const variables = {
    storeid: input.storeid,
    username: input.username,
    inMaxMiles: input.inMaxMiles,
    inMenuname: input.inMenuname,
    inMenuInterval: input.inMenuInterval,
    inMilesAfter: input.inMilesAfter,
    inMilesBefore: input.inMilesBefore,
    isdefault: input.isdefault,
    tenantId: input.tenantId,
  };
  return FetchConfig({
    anonymous: false,
    storeid: input.storeid,
    query: `mutation addMenu(
      $storeid: String
      $username: String
      $inMaxMiles: BigFloat
      $inMenuname: String
      $inMenuInterval: BigFloat
      $inMilesAfter: BigFloat
      $inMilesBefore: BigFloat
      $isdefault: BitString
      $tenantId: String
    ) {
      statelessServiceBzoInsertMenuMaster(
        input: {
          inStoreId: $storeid
          username: $username
          inMaxMiles: $inMaxMiles
          inMenuname: $inMenuname
          inMenuInterval: $inMenuInterval
          inMilesAfter: $inMilesAfter
          inMilesBefore: $inMilesBefore
          isdefault: $isdefault
          inTenantId: $tenantId
        }
      ) {
        clientMutationId
      }
    }`,
    variables: variables,
  }).then((response: IFetchConfigReturn) => {
    return response.data.statelessServiceBzoInsertMenuMaster;
  });
};

export const insertMenuDetails = (input: any, realmName: any) => {
  const variables = {
    storeid: input.storeid,
    username: input.username,
    menudata: input.menudata,
    inTenantId: input.tenantId,
  };
  return FetchConfig({
    anonymous: false,
    Realm: realmName,
    storeid: input.storeid,
    query: `mutation insertUser($menudata:  JSON!, $storeid: String, $username: String, $inTenantId: String) {
      statelessServiceBzoInsertMenuDetails(
        input: { menudata: $menudata, storeid: $storeid, username: $username, inTenantId: $inTenantId }
      ) {
        clientMutationId
      }
    }`,
    variables: variables,
  }).then((response: IFetchConfigReturn) => {
    return response.data.statelessServiceBzoInsertMenuDetails;
  });
};

export const deleteInterval = (input: any, realmName: any) => {
  const variables = {
    storeid: input.storeid,
    userName: input.username,
    inMenuname: input.inMenuname,
    serviceType: input.serviceType,
  };
  return FetchConfig({
    anonymous: false,
    Realm: realmName,
    storeid: input.storeid,
    query: ` mutation deleteMenuMaster(
      $storeid: String
      $userName: String
      $inMenuname: String
      $serviceType: Int
    ) {
      statelessCcPhysicalRwDeleteMenuMaster(
        input: {
          storeid: $storeid
          username: $userName
          inMenuname: $inMenuname
          serviceType: $serviceType
        }
      ) {
        clientMutationId
      }
    }`,
    variables: variables,
  }).then((response: IFetchConfigReturn) => {
    return response.data.statelessCcPhysicalRwDeleteMenuMaster;
  });
};

export const deleteMenu = (input: any, realmName: any) => {
  const variables = {
    storeid: input.storeid,
    userName: input.username,
    inMenuname: input.inMenuname,
    inTenantId: input.tenantId,
  };
  return FetchConfig({
    anonymous: false,
    storeid: input.storeid,
    query: ` mutation deleteMenu(
      $storeid: String
      $username: String
      $inMenuname: String
      $inTenantId: String
    ) {
      statelessServiceBzoDeleteMenuDetails(
        input: { storeid: $storeid, username: $username, inMenuname: $inMenuname, inTenantId: $inTenantId }
      ) {
        clientMutationId
      }
    }`,
    variables: variables,
  }).then((response: IFetchConfigReturn) => {
    return response.data.statelessServiceBzoDeleteMenuDetails;
  });
};

export const editMenu = (input: any, realmName: any) => {
  const variables = {
    storeid: input.storeid,
    username: input.username,
    inMenuname: input.inMenuname,
    isdefault: input.isdefault,
    inTenantId: input.tenantId,
  };
  return FetchConfig({
    anonymous: false,
    storeid: input.storeid,
    query: `mutation editMenu(
      $inMenuname: String
      $isdefault: BitString
      $storeid: String
      $username: String
      $inTenantId: String
    ) {
      statelessServiceBzoInsertMenuDefaultStatus(
        input: {
          inMenuname: $inMenuname
          isdefault: $isdefault
          storeid: $storeid
          username: $username
          inTenantId: $inTenantId
        }
      ) {
        clientMutationId
      }
    }`,
    variables: variables,
  }).then((response: IFetchConfigReturn) => {
    return response.data.statelessServiceBzoInsertMenuDefaultStatus;
  });
};

export const addNewAduUser = (input: any) => {
  const variables = {
    email: input.userName,
    firstname: input.first,
    lastname: input.last,
    userRole: input.role,
  };
  return FetchConfig({
    anonymous: false,
    query: `mutation addNewAduUser(
  $email: String!
  $firstname: String!
  $lastname: String!
  $userRole: String!
) {
  statelessServiceBzoCreateAduUsersInAllRealms(
    input: {
      email: $email
      firstname: $firstname
      lastname: $lastname
      userRole: $userRole
    }
  ) {
    string
  }
}`,
    variables: variables,
  }).then((response: IFetchConfigReturn) => {
    return JSON.parse(
      response.data.statelessServiceBzoCreateAduUsersInAllRealms.string
    );
  });
};

export const checkAduUser = async (input: any) => {
  const variables = {
    email: input.userName,
    firstname: input.first,
    lastname: input.last,
    userRole: input.role,
  };
  return FetchConfig({
    anonymous: false,
    query: `mutation checkAduUser(
  $email: String!
  $firstname: String!
  $lastname: String!
  $userRole: String!
) {
  statelessServiceBzoCheckAduUser(
    input: {
      email: $email
      firstname: $firstname
      lastname: $lastname
      userRole: $userRole
    }
  ) {
    string
  }
}`,
    variables: variables,
  }).then((response: IFetchConfigReturn) => {
    return JSON.parse(response.data.statelessServiceBzoCheckAduUser.string);
  });
};

export const deleteAduUser = async (input: any) => {
  const variables = {
    email: input,
  };
  return FetchConfig({
    anonymous: false,
    query: `mutation deleteAduUser(
              $email: String!
            ) {
              deleteAduUserAllRealms(
                input: {
                  email: $email
                }
            ) {
              json
            }
          }`,
    variables: variables,
  }).then((response: IFetchConfigReturn) => {
    return JSON.parse(response.data.deleteAduUserAllRealms.json);
  });
};

export const getCategorizedOpcodeStatus = (
  inDms: any,
  inStoreId: any,
  inTenantId: any
) => {
  const variables = {
    inDms: inDms,
    inStoreId: inStoreId,
    inTenantId: inTenantId,
  };
  return FetchConfig({
    anonymous: false,
    query: `mutation getCategorizedOpcodeStatus(
      $inDms: String!
      $inStoreId: String!
      $inTenantId:String!
      ) {
       
        statelessServiceBzoUserCategorizedOpcodeStatus(input: {
          inDms: $inDms,
          inStoreId: $inStoreId,
          inTenantId:$inTenantId}) {
           
    string
  
       }
    }
  `,
    variables: variables,
  }).then((response: IFetchConfigReturn) => {
    return response.data.statelessServiceBzoUserCategorizedOpcodeStatus;
  });
};
export const UpdateOpcodeCategorizationStatus = (
  inDms: any,
  inStoreId: any,
  inTenantId: any
) => {
  const variables = {
    inDms: inDms,
    inStoreId: inStoreId,
    inTenantId: inTenantId,
  };
  return FetchConfig({
    anonymous: false,
    query: `mutation UpdateSubmitStatus(
      $inDms: String!
      $inStoreId: String!
      $inTenantId:String!
      ) {
        statelessServiceBzoUpdateCategorizedOpcode(input: {
          inDms: $inDms,
          inStoreId: $inStoreId,
          inTenantId:$inTenantId
        }) {
            string    
       }
    }
  `,
    variables: variables,
  }).then((response: IFetchConfigReturn) => {
    return response.data.statelessServiceBzoUpdateCategorizedOpcode;
  });
};

export const getOpcodeCounts = (dms: any, inStoreId: any, inTenantId: any) => {
  const variables = {
    dms: dms,
    inStoreId: inStoreId,
    inTenantId: inTenantId,
  };
  return FetchConfig({
    anonymous: false,
    query: `mutation getOpcodeCounts(
      $dms: String!
      $inStoreId: String!
      $inTenantId:String!
      ) {
        statelessServiceBzoGetOnboardingOpcodeCategorizationCounts(input: {
          dms: $dms,
          inStoreId: $inStoreId,
          inTenantId:$inTenantId}) {
           
    results {
      categorizedCount
      competitiveCount
      maintenanceCount
      naCount
      repairCount
      totalCount
      uncategorizedCount
    }
  
       }
    }
  `,
    variables: variables,
  }).then((response: IFetchConfigReturn) => {
    return response.data
      .statelessServiceBzoGetOnboardingOpcodeCategorizationCounts;
  });
};

export const UpdateOpcodeCategoryChange = (
  pDms: any,
  pStoreId: any,
  pTenantId: any
) => {
  const variables = {
    pDms: pDms,
    pStoreId: pStoreId,
    pTenantId: pTenantId,
  };
  return FetchConfig({
    anonymous: false,
    query: `mutation UpdateOpcodeCategoryChange(
      $pDms: String!
      $pStoreId: String!
      $pTenantId:String!
      ) {
         statelessServiceBzoOpcodeOpcategoryChange(input: {
          pDms: $pDms,
          pStoreId: $pStoreId,
          pTenantId:$pTenantId
        }) {
            results {
      additionalDescription
      cprocount
      elr
      lbrlabortype
      lbropcode
      lbropcodedesc
      totallabordollars
      totalhours
      storeId
      retailqualsale
      retailqualhours
      opcategory
      id
      department
    }
       }
    }
  `,
    variables: variables,
  }).then((response: IFetchConfigReturn) => {
    return response.data.statelessServiceBzoOpcodeOpcategoryChange;
  });
};

export const Phase1Message = (record: any) => {
  const variables = {
    inDms: localStorage.getItem("dmsType"),
    inStoreId: record.storeId,
    inTenantId: record.tenantId,
  };
  return FetchConfig({
    anonymous: false,
    query: `mutation Phase1Message(
      $inDms: String!
      $inStoreId: String!
      $inTenantId:String!
      ) {
         statelessServiceBzoPhase1VerificationFailure(input: {
          inDms: $inDms,
          inStoreId: $inStoreId,
          inTenantId:$inTenantId
        }) {
           string
       }
    }
  `,
    variables: variables,
  }).then((response: IFetchConfigReturn) => {
    return response.data.statelessServiceBzoPhase1VerificationFailure;
  });
};

export const getDetailedLoginList = (fromDate: any, toDate: any) => {
  const variables = {
    fromDate: fromDate,
    toDate: toDate,
  };
  return FetchConfig({
    anonymous: false,
    query: `mutation getDetailedLoginList(
      $fromDate: Date
      $toDate: Date
      ) {
        statelessServiceReportsUserLoginAttemptsByDate(input: {
          fromDate: $fromDate,
          toDate: $toDate}) {
           
    
      json
    
  
       }
    }
  `,
    variables: variables,
  }).then((response: IFetchConfigReturn) => {
    return JSON.parse(
      response.data.statelessServiceReportsUserLoginAttemptsByDate.json
    );
  });
};

export const getDetailedLoginSummary = () => {
  return FetchConfig({
    anonymous: false,
    query: `
      mutation {
        statelessServiceReportsUserLoginStatsDblink(input: {}) {
          clientMutationId
          json
        }
      }
    `,
    variables: {},
  }).then((response: IFetchConfigReturn) => {
    return JSON.parse(
      response.data.statelessServiceReportsUserLoginStatsDblink.json
    );
  });
};

/* phase1 validation click */
export const phase1Validationclick = (
  dms: string,
  tenantId: string,
  storeId: string
) => {
  const variables = {
    inDms: dms,
    inTenantId: tenantId,
    inStoreId: storeId,
  };
  return FetchConfig({
    anonymous: false,
    query: `mutation phase1Validationclick(
      $inDms: String
      $inTenantId: String
      $inStoreId: String
      ) {
       phase1DataVerificationDetail(input: {inDms: $inDms, inTenantId: $inTenantId, inStoreId: $inStoreId}) {
           json
        }
    }
  `,
    variables: variables,
  }).then((response: IFetchConfigReturn) => {
    return JSON.parse(response.data.phase1DataVerificationDetail.json);
  });
};

export const opcodeCategorizationClick = (
  dms: string,
  tenantId: string,
  storeId: string
) => {
  const variables = {
    inDms: dms,
    inTenantId: tenantId,
    inStoreId: storeId,
  };
  return FetchConfig({
    anonymous: false,
    query: `mutation opcodeCategorizationClick(
      $inDms: String
      $inTenantId: String
      $inStoreId: String
      ) {
       setOpcodeCategorizations(input: {inDms: $inDms, inTenantId: $inTenantId, inStoreId: $inStoreId}) {
           json
        }
    }
  `,
    variables: variables,
  }).then((response: IFetchConfigReturn) => {
    return JSON.parse(response.data.setOpcodeCategorizations.json);
  });
};

export const opcodeValidationClick = (
  dms: string,
  tenantId: string,
  storeId: string
) => {
  const variables = {
    inDms: dms,
    inTenantId: tenantId,
    inStoreId: storeId,
  };
  return FetchConfig({
    anonymous: false,
    query: `mutation opcodeValidationClick(
      $inDms: String
      $inTenantId: String
      $inStoreId: String
      ) {
       opcodeCategorizationDataVerificationDetail(input: {inDms: $inDms, inTenantId: $inTenantId, inStoreId: $inStoreId}) {
           json
        }
    }
  `,
    variables: variables,
  }).then((response: IFetchConfigReturn) => {
    return JSON.parse(
      response.data.opcodeCategorizationDataVerificationDetail.json
    );
  });
};

export const phase2ValidationClick = (
  dms: string,
  tenantId: string,
  storeId: string
) => {
  const variables = {
    inDms: dms,
    inTenantId: tenantId,
    inStoreId: storeId,
  };
  return FetchConfig({
    anonymous: false,
    query: `mutation phase2ValidationClick(
      $inDms: String
      $inTenantId: String
      $inStoreId: String
      ) {
       phase2DataVerificationDetail(input: {inDms: $inDms, inTenantId: $inTenantId, inStoreId: $inStoreId}) {
           json
        }
    }
  `,
    variables: variables,
  }).then((response: IFetchConfigReturn) => {
    return JSON.parse(response.data.phase2DataVerificationDetail.json);
  });
};

const DataMutationQueries = {
  DeleteTenant,
  MutateDms,
  MutateGroupingMaster,
  InsertOrUpdateTenant,
  OpcodeMutation,
  InsertStore,
  StoreOnboardingEmail,
  updateTechnicianStatus,
  updatePayTypeMasterByCode,
  UpdateAdvisorDetails,
  GetSetStoreSettings,
  ConfigureTenant,
  ConfigureStore,
  InsertOrUpdatePartsMatrix,
  fetchKpiScorecardPartsMatrix,
  EnableDisableTenant,
  InsertUpdateLaborGrid,
  InsertGriddataDtl,
  FileUpload,
  UpdateBulkStatus,
  PartsMatrixFileUpload,
  UpdateSubmitStatus,
  getOpcodeCategoryStatus,
  InsertWorkPackage,
  InsertWorkPackageHierarchies,
  updateMatrixType,
  updateModels,
  addMenu,
  addNewAduUser,
  checkAduUser,
  getCategorizedOpcodeStatus,
  getOpcodeCounts,
  UpdateOpcodeCategoryChange,
  Phase1Message,
  getDetailedLoginList,
  getDetailedLoginSummary,
  phase1Validationclick,
  opcodeCategorizationClick,
  opcodeValidationClick,
  phase2ValidationClick,
};

export default DataMutationQueries;
