import FetchConfig from "../fetchConfig";
import { IFetchConfigReturn } from "../../types";
import { traceSpan } from "../../utils/OTTTracing";

const GetDashboardCardQuery = () => {
  return FetchConfig({
    anonymous: false,
    query: `query GetDashboardCardQuery {
        statelessServiceBzoTenants {
          nodes {
            isTestTenant
            cancelled
            onboarding
            nsQaValidation
            readyToLaunch
            launched
            review
            deletedstoreortenant
          }
        }
      }
      `,
  }).then((response: IFetchConfigReturn) => {
    // traceSpan(`Menu Load`, {
    //       event: `Menu Load`,
    //       pageUrl: window.location.pathname,
    //       timestamp: new Date().toISOString(),
    //       userId:localStorage.getItem('user') || ''
    //     })
    return response.data.statelessServiceBzoTenants.nodes;
  });
};

const TenantShowQuery = (id: number) => {
  const variables = {
    id: id,
  };
  return FetchConfig({
    anonymous: false,
    query: `query TenantShowQuery($id: Int!) {
        statelessServiceBzoTenants(condition: { id: $id }) {
          nodes {
            id
            tenantId
            tenantName
            subDomain
            isConfigured
            tenantImg
            infrastructureConfiguration
            agreementDate
            tenantDeletedDate
            count
            cancelled
            isTestTenant
            realmName
          }
        }
      }`,
    variables: variables,
  }).then((response: IFetchConfigReturn) => {
    // traceSpan(`click_newtelllll_button2222222`, {
    //       event: `click_nllllllll_button2222222222222`,
    //       pageUrl: window.location.pathname,
    //       value: variables,
    //       timestamp: new Date().toISOString(),
    //       userId:localStorage.getItem('user') || ''
    //     })
    return response.data.statelessServiceBzoTenants.nodes[0];
  });
};

const GetTenantQuery = (id: number) => {
  const variables = {
    id: id,
  };
  return FetchConfig({
    anonymous: false,
    query: `query GetTenantQuery($id: Int!) {
        statelessServiceBzoTenants(condition: { id: $id }) {
          nodes {
            id
            dmsList
            dms
            tenantId
            tenantName
            count
            subDomain
            isConfigured
            tenantImg
            infrastructureConfiguration
            agreementDate
            tenantDeletedDate
            deletedStoreCount
            activeStoreCount
            displayName
          }
        }
      }`,
    variables: variables,
  }).then((response: IFetchConfigReturn) => {
    // traceSpan(`click_newtelllll_button`, {
    //       event: `click_nllllllll_button`,
    //       pageUrl: window.location.pathname,
    //       value: variables,
    //       timestamp: new Date().toISOString(),
    //       userId:localStorage.getItem('user') || ''
    //     })
    return response.data.statelessServiceBzoTenants.nodes[0];
  });
};

const GetAllTenantsQuery = () => {
  return FetchConfig({
    anonymous: false,
    query: `query GetAllTenantsQuery{
        statelessServiceBzoTenants {
          nodes {
            id
            dms
            tenantId
            tenantName
            count
            realmName
          }
        }
      }`,
  }).then((response: IFetchConfigReturn) => {
    // traceSpan(`click_newtelllll_button333333333`, {
    //       event: `click_nllllllll_button3333`,
    //       pageUrl: window.location.pathname,
    //       timestamp: new Date().toISOString(),
    //       userId:localStorage.getItem('user') || ''
    //     })
    return response.data.statelessServiceBzoTenants.nodes;
  });
};

const GetAllTenantsListQuery = () => {
  return FetchConfig({
    anonymous: false,
    query: `query GetAllTenantsQuery{
        statelessServiceBzoTenants {
    nodes {
      activeStoreCount
      agreementDate
      infrastructureConfiguration
      isConfigured
      isTestTenant
      subDomain
      tenantName
    }
  }
      }`,
  }).then((response: IFetchConfigReturn) => {
    // traceSpan(`click_newtelllll_button444`, {
    //       event: `click_nllllllll_button444444`,
    //       pageUrl: window.location.pathname,
    //       timestamp: new Date().toISOString(),
    //       userId:localStorage.getItem('user') || ''
    //     })
    return response.data.statelessServiceBzoTenants.nodes;
  });
};

const GetAllTenantsRealmsQuery = () => {
  const variables = {
    isConfigured: true,
  };
  return FetchConfig({
    anonymous: false,
    query: `query GetAllTenantsQuery($isConfigured: Boolean ){
        statelessServiceBzoTenants( condition: {isConfigured: $isConfigured}) {
          nodes {
            tenantName
            realmName
          }
        }
      }`,
    variables: variables,
  }).then((response: IFetchConfigReturn) => {
    return response.data.statelessServiceBzoTenants.nodes;
  });
};

const GetAllTenantUsersQuery = () => {
  return FetchConfig({
    anonymous: false,
    query: `mutation GetAllTenantUsersQuery {
    statelessServiceBzoGetAllTenantsUsers(input: {}) {
    results {
      email
      realmName
      userName
      userRole
    }
  }
}`,
  }).then((response: IFetchConfigReturn) => {
    return response.data.statelessServiceBzoGetAllTenantsUsers.results;
  });
};

const GetUsersListByTenants = (realmName: string) => {
  const variables = {
    inRealmNames: realmName,
  };
  return FetchConfig({
    anonymous: false,
    query: `mutation GetUsersListByTenants($inRealmNames: [String]) {
  statelessServiceBzoGetUsersByTenants(input: {inRealmNames: $inRealmNames}) {
    clientMutationId
    results {
      email
      realmName
      userGroup
      userName
      userRole
    }
  }
}
`,
    variables: variables,
  }).then((response: IFetchConfigReturn) => {
    return response.data.statelessServiceBzoGetUsersByTenants.results;
  });
};

const GetBillingSubsciptionList = (tenantId: string) => {
  const variables = {
    inTenantId: tenantId,
  };
  return FetchConfig({
    anonymous: false,
    query: `mutation GetBillingSubsciptionList($inTenantId: String!) {
  statelessServiceBillingGetBillingSubscriptions(
    input: {inTenantId: $inTenantId}
  ) {
    statelessServiceBillingBillingSubscriptions {
      tenantId
      subscriptionId
      invoiceDate
      subscriptionStatus
      dmsOneTimeFee
      dmsMonthlyRecurringFee
      fopcMonthlyRecurringFee
      cancelledDate
      cancelledBy
      cancelledByDate
      cancellationConfirmedBy
      cancellationConfirmedByDate
      createdAt
      tenantName
      storeName
    }
  }
}
`,
    variables: variables,
  }).then((response: IFetchConfigReturn) => {
    return response.data.statelessServiceBillingGetBillingSubscriptions
      .statelessServiceBillingBillingSubscriptions;
  });
};

const GetSmokeTestRunList = () => {
  return FetchConfig({
    anonymous: false,
    query: `mutation MyMutation {
  statelessServiceReportsGetSmokeTestTenants(input: {}) {
    smokeTestTenants {
      storeId
      storeName
      subDomain
      tenantId
      tenantName
      testStatus
      userRole
    }
  }
}
`,
  }).then((response: IFetchConfigReturn) => {
    return response.data.statelessServiceReportsGetSmokeTestTenants
      .smokeTestTenants;
  });
};

const GetBugStatusList = (tenantName: string) => {
  const variables = {
    tenantName: tenantName,
  };
  return FetchConfig({
    anonymous: false,
    query: `mutation GetBugStatusList($tenantName: String!)  {
  statelessServiceBzoGetOpenprojectBugsList(
    input: {tenantName: $tenantName}
  ) {
    results {
      label
      value
      sortOrder
      category
    }
  }
}
`,
    variables: variables,
  }).then((response: IFetchConfigReturn) => {
    return response.data.statelessServiceBzoGetOpenprojectBugsList.results;
  });
};

const TenantQueries = {
  GetDashboardCardQuery,
  GetTenantQuery,
  TenantShowQuery,
  GetAllTenantsQuery,
  GetAllTenantUsersQuery,
  GetBillingSubsciptionList,
  GetSmokeTestRunList,
  GetUsersListByTenants,
  GetAllTenantsRealmsQuery,
  GetAllTenantsListQuery,
  GetBugStatusList,
};

export default TenantQueries;
