export class Constants {
  static DATA_FEEDS: any = {
    dtk: {
      enterpriseCode: "Enterprise Code",
      companyNumber: "Company Number",
      serverName: "Server Name",
      groupName: "Keycloak-Group Name",
      stateCode: "State Code",
      dmsType: "DMS",
      projectId: "Project Id",
      secondProjectId: "Second Project Id",
    },
    cdk: {
      groupName: "Keycloak-Group Name",
      dealerId: "Dealer Id",
      dmsType: "DMS",
      // startDate: "Start Date",
      // endDate: "End Date",
    },
    atm: {
      groupName: "Keycloak-Group Name",
      dealerId: "Dealer Id",
      dmsType: "DMS",
      // startDate: "Start Date",
      // endDate: "End Date",
      stateCode: "State Code",
      projectId: "Project Id",
    },
    Reynolds: {
      groupName: "Keycloak-Group Name",
      dealerId: "Dealer Id",
      dmsType: "DMS",
      // startDate: "Start Date",
      // endDate: "End Date",
      stateCode: "State Code",
      // projectId: "Project Id",
    },
    Tekion: {
      groupName: "Keycloak-Group Name",
      dealerId: "Dealer Id",
      dmsType: "DMS",
      stateCode: "State Code",
      // projectId: "Project Id",
    }
  };

  static patterns = {
    alphanumericStoreNameRegex: /^[a-zA-Z\s!@#$%^&*(),.?":{}|<>_-]*$/,
    addressRegex: /^[a-zA-Z0-9\s@#&()\-,.]*$/,
    alphabeticRegex: /^[a-zA-Z]*$/,
    alphaNumericRegex: /^[a-zA-Z0-9]*$/,
    websiteUrlregex:
      /^(https?:\/\/)?(www\.)?([a-zA-Z0-9_-]+\.){1,}[a-zA-Z]{2,}(\/[^\s]*)?(\?(\w+=\w+(&\w+=\w+)*)?)?$/,
    lowercaseAlphabetRegex: /^[a-z0-9]*$/,
    generateId: /[^a-z0-9]+/g,
    numericalRegex: /^(?:\d+)?(?:\.\d+)?$/,
    dealerId: /^[a-zA-Z0-9]+__[a-zA-Z0-9]+_[a-zA-Z0-9]+$/,
    gridName: /^(?!.*\s{2})(?!.*[^a-zA-Z0-9\s])(?!^\s)(?!.*\s$).{1,50}$/,
  };

  static actions = {
    delete: "delete",
    update: "update",
    get: "get",
    cancel: "cancel",
    edit: "edit",
    insert: "insert",
  };

  static checklistStatus = {
    ongoing: "Ongoing",
    pending: "Pending",
    completed: "Completed",
  };

  static success = "Success";
  static successCap = "SUCCESS";
  static nullText = "NULL";
  static store = "Store";
  static group = "Group";
  static groupingDetails = "Group Details";
  static dataFeedDetails = "Data Feed Details";
  static url = "url";
  static src = "src";
  static dms = "Dms";
  static type = { string: "string", object: "object" };
  static FOPCsubDomain = ".fixedopspc.com";
  static NA = "N/A";
  static partsMatrixGqlCallType = {
    update: "update",
    updateCountChange: "update_count_change",
    prtsourceList: "prtsource_list",
    delete: "delete",
    matrixType: "matrix_type",
    insert: "insert",
    matrix: "matrix",
  };
  static menuMappingGqlCallType = {
    unmapModel: "unmap_model",
    unmapMake: "unmap_make",
    map: "map",
  };
  static statusType = {
    success: "success",
    warning: "warning",
    error: "error",
  };
  static colors = {
    modalHeading: "#003d6b",
    greyBorder: "#c5c5c5",
    greyText: "#767676"
  }
  static ag_grid_theme = "ag-theme-balham";
}
