import React, { useEffect, useState } from "react";
import {
  Box,
  Grid,
  Typography,
  CircularProgress,
  Snackbar,
  Alert,
} from "@mui/material";
import CheckBoxOutlineBlankIcon from "@mui/icons-material/CheckBoxOutlineBlank";
import CheckBoxIcon from "@mui/icons-material/CheckBox";
import { useSidebarState } from "react-admin";
import {
  GetLaunchReportsListDetails,
  GetAduOpenStoreWorkpackages,
} from "../service/launchReportsList";
import ExampleComponent from "./HandsonTable";

const LaunchReportGrid = () => {
  const [sidebarIsOpen] = useSidebarState();
  const [allMenu, setAllMenu] = useState({});
  const [selectedKey, setSelectedKey] = useState("onboardingTotal");
  const [tableData, setTableData] = useState([]);
  const [allTypeValue, setAllTypeValue] = useState([]);
  const [loading, setLoading] = useState(false);
  const [refreshTrigger, setRefreshTrigger] = useState(false);
  const [errorMessage, setErrorMessage] = useState("");

  useEffect(() => {
    fetchLaunchReportData();
  }, [refreshTrigger]);

  useEffect(() => {
    fetchTableData();
  }, [selectedKey, refreshTrigger]);

  const handleRefreshTrigger = () => {
    setRefreshTrigger((prev) => !prev);
  };

  const fetchLaunchReportData = async () => {
    try {
      const res = await GetLaunchReportsListDetails();
      if (res && Array.isArray(res) && res[0]) {
        setAllMenu(res[0]);
      }
    } catch (error) {
      console.error(error);
      setErrorMessage("⚠️ Failed to load dashboard data.");
    }
  };

  const fetchTableData = async () => {
    setLoading(true);
    try {
      const res = await GetAduOpenStoreWorkpackages();
      if (!Array.isArray(res)) throw new Error("API did not return array");
      setAllTypeValue(res);

      let filtered = [];
      switch (selectedKey) {
        case "onboardingTotal":
          filtered = res.filter((i) => i.launchCompleted == null);
          break;
        case "launchCompleted":
          filtered = res.filter((i) => i.launchCompleted != null);
          break;
        case "cancellations":
          filtered = res.filter((i) => i.cancellationDate != null);
          break;
        default:
          filtered = res;
      }

      setTableData(filtered);
    } catch (error) {
      console.error("fetchTableData error:", error);
      setErrorMessage(
        "⚠️ Failed to fetch launch progress report data. Please try again."
      );
    } finally {
      setLoading(false);
    }
  };

  const cardData = [
    {
      key: "onboardingTotal",
      title: `Onboarding Total : ${allMenu.onboardingTotal || 0}`,
      color: "#2196F3",
      items: [
        { label: "Needs OB Meeting", value: allMenu.needsObMeeting },
        { label: "DMS Not Active", value: allMenu.dmsNotActive },
        { label: "Ops Not Categorized", value: allMenu.opsNotCategorized },
        { label: "Grid Not Entered", value: allMenu.gridNotEntered },
        { label: "Models Not Mapped", value: allMenu.modelsNotMapped },
        { label: "Matrix Not Entered", value: allMenu.matrixNotEntered },
        { label: "Users Not Created", value: allMenu.usersNotCreated },
      ],
    },
    {
      key: "launchCompleted",
      title: `Launch Completed : ${allMenu.launchCompleted || 0}`,
      color: "#F57C00",
      items: [
        { label: "Active Billing", value: allMenu.activeBilling },
        { label: "Trials", value: allMenu.trials },
        { label: "Trial Conversions", value: allMenu.trialConversions },
        { label: "3PP", value: allMenu.thirdPartyProviders },
      ],
    },
    {
      key: "cancellations",
      title: `Cancellations : ${allMenu.cancellations || 0}`,
      color: "#C62828",
      items: [
        {
          label: "Pre-Launch Cancellations",
          value: allMenu.preLaunchCancellations,
        },
        {
          label: "Post-Launch Cancellations",
          value: allMenu.postLaunchCancellations,
        },
        { label: "Trial Cancellations", value: allMenu.trialCancellations },
      ],
    },
  ];

  return (
    <Box
      sx={{
        paddingX: "10px",
        mt: "15px",
        width: "100%",
        "@media (max-width: 1440px)": { width: "83vw" },
      }}>
      {/* ====== Summary Cards ====== */}
      <Grid container spacing={2} width="100%">
        {cardData.map((card) => (
          <Grid item xs={12} md={4} key={card.key}>
            <Box
              onClick={() => !loading && setSelectedKey(card.key)}
              sx={{
                backgroundColor: card.color,
                color: "white",
                borderRadius: 2,
                p: 2,
                boxShadow: 3,
                cursor: "pointer",
                minHeight: 200,
                "&:hover": {
                  transform: "scale(1.02)",
                  transition: "transform 0.2s ease-in-out",
                },
              }}>
              <Box display="flex" alignItems="center" mb={1}>
                {selectedKey === card.key ? (
                  <CheckBoxIcon sx={{ color: "white", mr: 1 }} />
                ) : (
                  <CheckBoxOutlineBlankIcon sx={{ color: "white", mr: 1 }} />
                )}
                <Typography variant="h6" fontWeight="bold">
                  {card.title}
                </Typography>
              </Box>

              <Box sx={{ pl: 3 }}>
                {card.items.map((item, idx) => (
                  <Typography key={idx} variant="body2">
                    {item.label}: {item.value || 0}
                  </Typography>
                ))}
              </Box>
            </Box>
          </Grid>
        ))}
      </Grid>

      {/* ====== Handsontable Section ====== */}
      {selectedKey && (
        <Box sx={{ mt: 2, width: "100%" }}>
          <div
            id="hand-table"
            className={sidebarIsOpen ? "sidebar-open" : "sidebar-closed"}
            style={{ width: "80vw", marginBottom: 0, paddingBottom: 0 }}>
            {loading ? (
              <Box
                display="flex"
                justifyContent="center"
                alignItems="center"
                height={200}>
                <CircularProgress />
              </Box>
            ) : (
              <ExampleComponent
                tableData={tableData}
                selectedKey={selectedKey}
                allTypeValue={allTypeValue}
                refreshTrigger={handleRefreshTrigger}
              />
            )}
          </div>
        </Box>
      )}

      {/* ====== Snackbar for Errors ====== */}
      <Snackbar
        open={!!errorMessage}
        autoHideDuration={8000}
        onClose={() => setErrorMessage("")}
        anchorOrigin={{ vertical: "bottom", horizontal: "right" }}>
        <Alert
          onClose={() => setErrorMessage("")}
          severity="error"
          sx={{ width: "100%" }}>
          {errorMessage}
        </Alert>
      </Snackbar>
    </Box>
  );
};

export default LaunchReportGrid;
