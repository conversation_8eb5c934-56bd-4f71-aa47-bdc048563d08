import React, { useState, useEffect, ReactElement } from "react";
import { MenuItemLink, useTranslate, useSidebarState } from "react-admin";
import { Box } from "@mui/material";
import { useLocation } from "react-router-dom";
import {
  Dashboard as DashboardIcon,
  Store as StoreIcon,
  EventBusy as EventBusyIcon,
  Quiz as QuizIcon,
  Assignment as AssignmentIcon,
  History as HistoryIcon,
  InfoOutlined as InfoIcon,
  Checklist as ChecklistIcon,
  RateReview as RateReviewIcon,
  Recommend as RecommendIcon,
  DeleteSweep as DeleteSweepIcon,
  Engineering as EngineeringIcon,
  ViewModule as ViewModuleIcon,
  AutoGraph as AutoGraphIcon,
  CancelPresentationOutlined as CancelPresentationOutlinedIcon,
  Build as BuildIcon,
  Autorenew as AutorenewIcon,
  AutoModeOutlined as AutoModeOutlinedIcon,
  GroupsOutlined as GroupsOutlinedIcon,
  Bar<PERSON>hart as BarChartIcon,
  Person as PersonIcon,
  ViewList as ViewListIcon,
  Settings as SettingsIcon,
  Storage as StorageIcon,
  PermDataSetting as PermDataSettingIcon,
} from "@mui/icons-material";
import SubMenu from "../Layout/SubMenu";
import { PageRoutes } from "../utils/pageRoutes";
import { traceSpan } from "../utils/OTTTracing";

const realmRole = JSON.parse(localStorage.getItem("role") || "{}");

type MenuItem = {
  to: string;
  text: string;
  icon: ReactElement;
};

type SubMenuProps = {
  menu: MenuName;
  name: string;
  icon: ReactElement;
  items: MenuItem[];
};
type MenuProps = {
  menu: MenuName;
  name: string;
  icon: ReactElement;
  to: string;
};

type MenuName =
  | "menuDashboard"
  | "menuStores"
  | "menuNeverEvents"
  | "menuTestResults"
  | "menuSettings"
  | "menuTelemetry"
  | "menuLauchReport"
  | "menuActivitylog";

const SideMenu: React.FC<any> = ({ onMenuClick, logout }) => {
  const translate = useTranslate();
  const location = useLocation();
  const [state, setState] = useState<Record<MenuName, boolean>>({
    menuDashboard: false,
    menuNeverEvents: false,
    menuStores: false,
    menuTestResults: false,
    menuSettings: false,
    menuTelemetry: false,
    menuLauchReport: false,
    menuActivitylog: false,
  });
  const [selectedSubMenu, setSelectedSubMenu] = useState<string | null>(null);

  useEffect(() => {
    // Retrieve selected submenu from local storage
    const savedSubMenu = localStorage.getItem("selectedSubMenu");
    if (savedSubMenu) {
      setSelectedSubMenu(savedSubMenu);
    }
  }, []);

  useEffect(() => {
    // Update state based on selected submenu
    const currentPath = location.pathname;
    const matchedSubMenu =
      menuItems
        .flatMap((menu) => menu.items)
        .find((item) => item.to === currentPath)?.text || null;
    if (matchedSubMenu) {
      setSelectedSubMenu(matchedSubMenu);
      localStorage.setItem("selectedSubMenu", matchedSubMenu);
    } else {
      const matchedMenu = mainMenuItems.find((item) => item.to === currentPath);
      const menuName = matchedMenu ? matchedMenu.name : null;
      setSelectedSubMenu(menuName);
      localStorage.setItem("selectedSubMenu", menuName as string);
    }
  }, [location.pathname]);

  const handleToggle = (menu: MenuName) => {
    traceSpan(`click_sidemenu_${menu}`, {
      event: `click_sidemenu_${menu}`,
      pageUrl: window.location.pathname,
      timestamp: new Date().toISOString(),
      userId: localStorage.getItem("user") || "",
    });
    setState((prevState) => ({ ...prevState, [menu]: !prevState[menu] }));
  };

  const handleMenuItemClick = (submenuName: string) => {
    // Save selected submenu to local storage
    traceSpan(`click_sidemenu_${submenuName}`, {
      event: `click_sidemenu_${submenuName}`,
      pageUrl: window.location.pathname,
      timestamp: new Date().toISOString(),
      userId: localStorage.getItem("user") || "",
    });
    setSelectedSubMenu(submenuName);
  };

  let menuItems: SubMenuProps[] = [
    {
      menu: "menuDashboard",
      name: translate("SIDEBAR.MENU_DASHBOARD.NAME"),
      icon: <DashboardIcon style={{ color: "#ffff", fontSize: "1.5rem" }} />,
      items: [
        {
          to: PageRoutes.onboarding,
          text: translate("SIDEBAR.MENU_DASHBOARD.ITEMS.ONBOARDING"),
          icon: <InfoIcon />,
        },
        {
          to: PageRoutes.nsQaValidation,
          text: translate("SIDEBAR.MENU_DASHBOARD.ITEMS.NS_QA_VALIDATION"),
          icon: <ChecklistIcon />,
        },
        {
          to: PageRoutes.review,
          text: translate("SIDEBAR.MENU_DASHBOARD.ITEMS.IN_REVIEW"),
          icon: <RateReviewIcon />,
        },
        {
          to: PageRoutes.readyToLaunch,
          text: translate("SIDEBAR.MENU_DASHBOARD.ITEMS.READY_TO_LAUNCH"),
          icon: <RecommendIcon />,
        },
        {
          to: PageRoutes.launched,
          text: translate("SIDEBAR.MENU_DASHBOARD.ITEMS.LAUNCHED"),
          icon: <ChecklistIcon />,
        },
        {
          to: PageRoutes.cancelled,
          text: translate("SIDEBAR.MENU_DASHBOARD.ITEMS.CANCELLED"),
          icon: <DeleteSweepIcon />,
        },
        {
          to: PageRoutes.testTenant,
          text: translate("SIDEBAR.MENU_DASHBOARD.ITEMS.TEST_TENANT"),
          icon: <EngineeringIcon />,
        },
        {
          to: PageRoutes.allTenantsDashboard,
          text: translate("SIDEBAR.MENU_DASHBOARD.ITEMS.ALLTENANTS"),
          icon: <ViewListIcon />,
        },
      ],
    },
    {
      menu: "menuStores",
      name: translate("SIDEBAR.MENU_STORES.NAME"),
      icon: <StoreIcon style={{ color: "#ffff", fontSize: "1.5rem" }} />,
      items: [
        {
          to: PageRoutes.allStores,
          text: translate("SIDEBAR.MENU_STORES.ITEMS.ALL_STORES"),
          icon: <StoreIcon />,
        },
        {
          to: PageRoutes.allStoresDetails,
          text: translate("SIDEBAR.MENU_STORES.ITEMS.ALL_MODULE_DETAILS"),
          icon: <ViewModuleIcon />,
        },
      ],
    },
    {
      menu: "menuNeverEvents",
      name: translate("SIDEBAR.MENU_NEVER_EVENTS.NAME"),
      icon: <EventBusyIcon style={{ color: "#ffff", fontSize: "1.5rem" }} />,
      items: [
        {
          to: PageRoutes.neverEventsEDI,
          text: translate("SIDEBAR.MENU_NEVER_EVENTS.ITEMS.EDI_EVENTS"),
          icon: <AutoGraphIcon />,
        },
        {
          to: PageRoutes.neverEventsFOPC,
          text: translate("SIDEBAR.MENU_NEVER_EVENTS.ITEMS.FOPC_EVENTS"),
          icon: <CancelPresentationOutlinedIcon />,
        },
        {
          to: PageRoutes.neverEventsMaintainance,
          text: translate("SIDEBAR.MENU_NEVER_EVENTS.ITEMS.MAINTENANCE_EVENTS"),
          icon: <BuildIcon />,
        },
        {
          to: PageRoutes.neverEventsUserRole,
          text: translate("SIDEBAR.MENU_NEVER_EVENTS.ITEMS.USER_ROLES"),
          icon: <GroupsOutlinedIcon />,
        },
        {
          to: PageRoutes.dailyDataAsOf,
          text: translate("SIDEBAR.MENU_NEVER_EVENTS.ITEMS.DAILY_DATA_AS_OF"),
          icon: <BarChartIcon />,
        },
        {
          to: PageRoutes.dailyLogins,
          text: translate("SIDEBAR.MENU_NEVER_EVENTS.ITEMS.DAILY_LOGINS"),
          icon: <PersonIcon />,
        },
        {
          to: PageRoutes.aduUsers,
          text: translate("SIDEBAR.MENU_NEVER_EVENTS.ITEMS.ADU_USERS"),
          icon: <PersonIcon />,
        },
        {
          to: PageRoutes.emailReport,
          text: translate("SIDEBAR.MENU_NEVER_EVENTS.ITEMS.EMAIL_REPORT"),
          icon: <BarChartIcon />,
        },
        {
          to: PageRoutes.ro13MonthReport,
          text: translate("SIDEBAR.MENU_NEVER_EVENTS.ITEMS.MNTHRO13_REPORT"),
          icon: <BarChartIcon />,
        },
        {
          to: PageRoutes.missedReport,
          text: translate("SIDEBAR.MENU_NEVER_EVENTS.ITEMS.MISSED_REPORT"),
          icon: <BarChartIcon />,
        },
      ],
    },
    {
      menu: "menuTestResults",
      name: translate("SIDEBAR.MENU_TEST_RESULTS.NAME"),
      icon: <QuizIcon style={{ color: "#ffff", fontSize: "1.5rem" }} />,
      items: [
        // {
        //   to: PageRoutes.regressionTest,
        //   text: translate("SIDEBAR.MENU_TEST_RESULTS.ITEMS.REGRESSION_TEST"),
        //   icon: <AutorenewIcon />,
        // },
        {
          to: PageRoutes.smokeTest,
          text: translate("SIDEBAR.MENU_TEST_RESULTS.ITEMS.SMOKE_TEST"),
          icon: <AutoModeOutlinedIcon />,
        },
      ],
    },
    {
      menu: "menuSettings",
      name: translate("SIDEBAR.MENU_SETTINGS.NAME"),
      icon: <SettingsIcon style={{ color: "#ffff", fontSize: "1.5rem" }} />,
      items: [
        {
          to: PageRoutes.dbSettings,
          text: translate("SIDEBAR.MENU_SETTINGS.ITEMS.DB_Settings"),
          icon: <StorageIcon />,
        },
        {
          to: PageRoutes.buildApplication,
          text: translate("SIDEBAR.MENU_SETTINGS.ITEMS.Build_Application"),
          icon: <StorageIcon />,
        },
        {
          to: PageRoutes.schemaConfig,
          text: translate("SIDEBAR.MENU_SETTINGS.ITEMS.SCHEMA_CONFIG"),
          icon: <PermDataSettingIcon />,
        },
      ],
    },
    {
      menu: "menuTelemetry",
      name: translate("SIDEBAR.MENU_TELEMETRY.NAME"),
      icon: <AutoGraphIcon style={{ color: "#ffff", fontSize: "1.5rem" }} />,
      items: [
        {
          to: PageRoutes.fopcTelemetry,
          text: translate("SIDEBAR.MENU_TELEMETRY.ITEMS.FOPC_Telemetry"),
          icon: <BarChartIcon />,
        },
        {
          to: PageRoutes.bzoTelemetry,
          text: translate("SIDEBAR.MENU_TELEMETRY.ITEMS.BZO_Telemetry"),
          icon: <AutoGraphIcon />,
        },
      ],
    },
  ];

  // ✅ Hide testTenant and Settings under Dashboard for non-superadmin

  if (realmRole !== "superadmin") {
    menuItems = menuItems.filter(
      (menu) => menu.menu !== "menuTestResults" && menu.menu !== "menuSettings"
    );

    menuItems = menuItems.map((menu) => {
      if (menu.menu === "menuDashboard") {
        return {
          ...menu,
          items: menu.items.filter((item) => item.to !== PageRoutes.testTenant),
        };
      }
      return menu;
    });
  }
  const mainMenuItems: MenuProps[] = [
    {
      menu: "menuLauchReport",
      name: translate("SIDEBAR.LAUNCH_REPORT"),
      icon: <AssignmentIcon style={{ color: "#ffff", fontSize: "1.5rem" }} />,
      to: PageRoutes.launchReports,
    },
    {
      menu: "menuActivitylog",
      name: translate("SIDEBAR.ACTIVITY_LOG"),
      icon: <HistoryIcon style={{ color: "#ffff", fontSize: "1.5rem" }} />,
      to: PageRoutes.activityLog,
    },
  ];
  const renderMenuItems = (items: MenuItem[]) =>
    items.map(({ to, text, icon }) => (
      <MenuItemLink
        key={to}
        to={to}
        primaryText={text}
        leftIcon={React.cloneElement(icon, {
          style: {
            color: selectedSubMenu === text ? "#1976d2" : "#ffffff",
            fontSize: "1.5rem",
          },
        })}
        onClick={() => handleMenuItemClick(text)}
        sx={{
          color: selectedSubMenu === text ? "#1976d2" : "#ffffff",
          bgcolor: selectedSubMenu === text ? "#ddeaf4" : "inherit",
          "&.RaMenuItemLink-active": {
            color: selectedSubMenu === text ? "#1976d2" : "#ffffff",
          },
          "&:hover": {
            color: "#1976d2",
            bgcolor: "#ddeaf4",
          },
        }}
        //placeholder="" // Optionally include if required
        // onPointerEnterCapture={() => {}} // Optionally include if required
        // onPointerLeaveCapture={() => {}} // Optionally include if required
      />
    ));

  return (
    <Box
      sx={{
        width: 250,
        bgcolor: "primary.main",
        height: "100vh",
        // position: "fixed",
        left: 0,
        paddingTop: "25px",
        zIndex: 1000,
        overflow: "auto",
      }}>
      {menuItems.map(({ menu, name, icon, items }) => {
        const isOpen =
          state[menu] || items.some((item) => item.text === selectedSubMenu);

        return (
          <SubMenu
            key={menu}
            handleToggle={() => handleToggle(menu)}
            isOpen={isOpen}
            name={name}
            icon={icon}
            dense={false}>
            {renderMenuItems(items)}
          </SubMenu>
        );
      })}

      <MenuItemLink
        to={PageRoutes.launchReports}
        primaryText={translate("SIDEBAR.LAUNCH_REPORT")}
        leftIcon={
          <AssignmentIcon
            style={{
              color:
                selectedSubMenu === "Launch Report" ? "#1976d2" : "#ffffff",
              fontSize: "1.5rem",
            }}
          />
        }
        onClick={() => handleMenuItemClick("Launch Reports")}
        sx={{
          color: selectedSubMenu === "Launch Report" ? "#1976d2" : "#ffffff",
          bgcolor: selectedSubMenu === "Launch Report" ? "#ddeaf4" : "inherit",
          "&.RaMenuItemLink-active": {
            color: "#1976d2",
          },
          "&:hover": {
            color: "#1976d2",
            bgcolor: "#ddeaf4",
          },
        }}
        //placeholder="" // Optionally include if required
        // onPointerEnterCapture={() => {}} // Optionally include if required
        // onPointerLeaveCapture={() => {}} // Optionally include if required
      />

      {/* We are not using DMSBilling feature currently. Holding this for future consideration. */}
      {/* <MenuItemLink
        to={PageRoutes.dmsBilling}
        primaryText={translate("SIDEBAR.DMS_BILLINGS")}
        leftIcon={
          <AttachMoneyIcon
            style={{
              color: selectedSubMenu === "DMS Billing" ? "#1976d2" : "#ffffff",
              fontSize: "1.5rem",
            }}
          />
        }
        onClick={() => handleMenuItemClick("DMS Billing")}
        sx={{
          color: selectedSubMenu === "DMS Billing" ? "#1976d2" : "#ffffff",
          bgcolor: selectedSubMenu === "DMS Billing" ? "#ddeaf4" : "inherit",
          "&.RaMenuItemLink-active": {
            color: "#1976d2",
          },
          "&:hover": {
            color: "#1976d2",
            bgcolor: "#ddeaf4",
          },
        }}
      /> */}
      <MenuItemLink
        to={PageRoutes.activityLog}
        primaryText={translate("SIDEBAR.ACTIVITY_LOG")}
        leftIcon={
          <HistoryIcon
            style={{
              color: selectedSubMenu === "Activity Log" ? "#1976d2" : "#ffffff",
              fontSize: "1.5rem",
            }}
          />
        }
        onClick={() => handleMenuItemClick("Activity Log")}
        sx={{
          color: selectedSubMenu === "Activity Log" ? "#1976d2" : "#ffffff",
          bgcolor: selectedSubMenu === "Activity Log" ? "#ddeaf4" : "inherit",
          "&.RaMenuItemLink-active": {
            color: "#1976d2",
          },
          "&:hover": {
            color: "#1976d2",
            bgcolor: "#ddeaf4",
          },
        }}
        //placeholder="" // Optionally include if required
        // onPointerEnterCapture={() => {}} // Optionally include if required
        // onPointerLeaveCapture={() => {}} // Optionally include if required
      />

      {/* <MenuItemLink
        to={"./"}
        primaryText={translate("SIDEBAR.DMS_SETTINGS")}
        leftIcon={
          <SettingsIcon
            style={{
              color: selectedSubMenu === "DMS Settings" ? "#1976d2" : "#ffffff",
              fontSize: "1.5rem",
            }}
          />
        }
        onClick={() => handleMenuItemClick("DMS Settings")}
        sx={{
          color: selectedSubMenu === "DMS Settings" ? "#1976d2" : "#ffffff",
          bgcolor: selectedSubMenu === "DMS Settings" ? "#ddeaf4" : "inherit",
          "&.RaMenuItemLink-active": {
            color: "#1976d2",
          },
          "&:hover": {
            color: "#1976d2",
            bgcolor: "#ddeaf4",
          },
        }}
        placeholder="" // Optionally include if required
        onPointerEnterCapture={() => {}} // Optionally include if required
        onPointerLeaveCapture={() => {}} // Optionally include if required
      /> */}
    </Box>
  );
};

export default SideMenu;
